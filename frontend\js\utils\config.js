/**
 * Configuration file for Preg and Baby Care application
 */

// API Configuration
const API_CONFIG = {
    BASE_URL: 'http://localhost:5000/api',
    TIMEOUT: 30000, // 30 seconds
    RETRY_ATTEMPTS: 3,
    RETRY_DELAY: 1000, // 1 second
};

// Application Configuration
const APP_CONFIG = {
    NAME: 'Preg and Baby Care',
    VERSION: '1.0.0',
    ENVIRONMENT: 'development', // development, staging, production
    DEBUG: true,
    
    // Feature flags
    FEATURES: {
        CHATBOT: true,
        WEIGHT_TRACKING: true,
        MEAL_PLANS: true,
        APPOINTMENTS: true,
        NOTIFICATIONS: true,
        SOCIAL_LOGIN: true,
        OFFLINE_MODE: false,
    },
    
    // UI Configuration
    UI: {
        THEME: 'light', // light, dark, auto
        LANGUAGE: 'en',
        TIMEZONE: 'UTC',
        DATE_FORMAT: 'YYYY-MM-DD',
        TIME_FORMAT: 'HH:mm',
        CURRENCY: 'USD',
    },
    
    // Pagination
    PAGINATION: {
        DEFAULT_PAGE_SIZE: 10,
        MAX_PAGE_SIZE: 100,
        SHOW_SIZE_OPTIONS: [5, 10, 20, 50],
    },
    
    // File Upload
    FILE_UPLOAD: {
        MAX_SIZE: 5 * 1024 * 1024, // 5MB
        ALLOWED_TYPES: ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'],
        CHUNK_SIZE: 1024 * 1024, // 1MB chunks
    },
    
    // Validation
    VALIDATION: {
        PASSWORD_MIN_LENGTH: 8,
        PASSWORD_REQUIRE_UPPERCASE: true,
        PASSWORD_REQUIRE_LOWERCASE: true,
        PASSWORD_REQUIRE_NUMBERS: true,
        PASSWORD_REQUIRE_SYMBOLS: false,
        EMAIL_REGEX: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
        PHONE_REGEX: /^\+?[\d\s\-\(\)]+$/,
    },
    
    // Local Storage Keys
    STORAGE_KEYS: {
        AUTH_TOKEN: 'preg_baby_care_token',
        REFRESH_TOKEN: 'preg_baby_care_refresh_token',
        USER_DATA: 'preg_baby_care_user',
        PREFERENCES: 'preg_baby_care_preferences',
        CHAT_HISTORY: 'preg_baby_care_chat_history',
        WEIGHT_DATA: 'preg_baby_care_weight_data',
        MEAL_PLANS: 'preg_baby_care_meal_plans',
        THEME: 'preg_baby_care_theme',
        LANGUAGE: 'preg_baby_care_language',
    },
    
    // Session Configuration
    SESSION: {
        TIMEOUT: 24 * 60 * 60 * 1000, // 24 hours
        WARNING_TIME: 5 * 60 * 1000, // 5 minutes before timeout
        REFRESH_INTERVAL: 15 * 60 * 1000, // 15 minutes
    },
    
    // Notification Configuration
    NOTIFICATIONS: {
        POSITION: 'top-right', // top-left, top-right, bottom-left, bottom-right
        DURATION: 5000, // 5 seconds
        MAX_NOTIFICATIONS: 5,
        SOUND_ENABLED: true,
    },
    
    // Chart Configuration
    CHARTS: {
        DEFAULT_COLORS: [
            '#e91e63', // Primary
            '#2196f3', // Secondary
            '#ff9800', // Accent
            '#4caf50', // Success
            '#f44336', // Error
            '#9c27b0', // Purple
            '#00bcd4', // Cyan
            '#795548', // Brown
        ],
        ANIMATION_DURATION: 1000,
        RESPONSIVE: true,
    },
    
    // Map Configuration (if needed for location features)
    MAPS: {
        DEFAULT_ZOOM: 10,
        DEFAULT_CENTER: { lat: 40.7128, lng: -74.0060 }, // New York City
        MARKER_CLUSTER: true,
    },
    
    // Social Media Links
    SOCIAL_LINKS: {
        FACEBOOK: 'https://facebook.com/pregbabycare',
        TWITTER: 'https://twitter.com/pregbabycare',
        INSTAGRAM: 'https://instagram.com/pregbabycare',
        YOUTUBE: 'https://youtube.com/pregbabycare',
        LINKEDIN: 'https://linkedin.com/company/pregbabycare',
    },
    
    // Contact Information
    CONTACT: {
        EMAIL: '<EMAIL>',
        PHONE: '******-PREG-CARE',
        ADDRESS: '123 Healthcare Ave, Medical City, MC 12345',
        SUPPORT_HOURS: 'Monday - Friday, 9 AM - 6 PM EST',
    },
    
    // Emergency Contacts
    EMERGENCY: {
        HOTLINE: '911',
        PREGNANCY_HELPLINE: '1-800-PREGNANCY',
        POISON_CONTROL: '1-************',
        MENTAL_HEALTH: '988', // Suicide & Crisis Lifeline
    },
};

// API Endpoints
const API_ENDPOINTS = {
    // Authentication
    AUTH: {
        LOGIN: '/auth/login',
        REGISTER: '/auth/register',
        LOGOUT: '/auth/logout',
        REFRESH: '/auth/refresh',
        PROFILE: '/auth/profile',
        CHANGE_PASSWORD: '/auth/change-password',
        FORGOT_PASSWORD: '/auth/forgot-password',
        RESET_PASSWORD: '/auth/reset-password',
        VERIFY_EMAIL: '/auth/verify-email',
    },
    
    // User Management
    USERS: {
        PROFILE: '/users/profile',
        UPDATE_PROFILE: '/users/profile',
        PREFERENCES: '/users/preferences',
        AVATAR: '/users/avatar',
        DELETE_ACCOUNT: '/users/delete',
    },
    
    // Nutrition
    NUTRITION: {
        PLANS: '/nutrition/plans',
        RECOMMENDATIONS: '/nutrition/recommendations',
        FOOD_SAFETY: '/nutrition/food-safety',
        DAILY_REQUIREMENTS: '/nutrition/daily-requirements',
    },
    
    // Weight Tracking
    WEIGHT: {
        RECORDS: '/weight/records',
        ADD_RECORD: '/weight/records',
        UPDATE_RECORD: '/weight/records',
        DELETE_RECORD: '/weight/records',
        EXPECTED_GAIN: '/weight/expected-gain',
        STATISTICS: '/weight/statistics',
    },
    
    // Meal Plans
    MEAL_PLANS: {
        DAILY: '/meal-plans/daily',
        WEEKLY: '/meal-plans/weekly',
        TODAY: '/meal-plans/today',
        SHOPPING_LIST: '/meal-plans/shopping-list',
        FAVORITES: '/meal-plans/favorites',
    },
    
    // Chatbot
    CHATBOT: {
        CHAT: '/chatbot/chat',
        HISTORY: '/chatbot/history',
        SESSIONS: '/chatbot/sessions',
        QUICK_QUESTIONS: '/chatbot/quick-questions',
        FEEDBACK: '/chatbot/feedback',
    },
    
    // Appointments
    APPOINTMENTS: {
        LIST: '/appointments',
        CREATE: '/appointments',
        UPDATE: '/appointments',
        DELETE: '/appointments',
        DOCTORS: '/appointments/doctors',
        AVAILABILITY: '/appointments/availability',
    },
    
    // Baby Care
    BABY_CARE: {
        INFO: '/baby-care/info',
        SLEEP_PATTERNS: '/baby-care/sleep-patterns',
        VACCINATIONS: '/baby-care/vaccinations',
        MILESTONES: '/baby-care/milestones',
        GROWTH_CHARTS: '/baby-care/growth-charts',
    },
    
    // Admin (if user has admin role)
    ADMIN: {
        USERS: '/admin/users',
        NUTRITION_PLANS: '/admin/nutrition-plans',
        MEAL_PLANS: '/admin/meal-plans',
        CONTENT: '/admin/content',
        ANALYTICS: '/admin/analytics',
    },
    
    // File Upload
    UPLOAD: {
        IMAGE: '/upload/image',
        DOCUMENT: '/upload/document',
        AVATAR: '/upload/avatar',
    },
    
    // Health Check
    HEALTH: '/health',
    INFO: '/info',
};

// Error Messages
const ERROR_MESSAGES = {
    NETWORK_ERROR: 'Network error. Please check your internet connection.',
    SERVER_ERROR: 'Server error. Please try again later.',
    UNAUTHORIZED: 'You are not authorized to perform this action.',
    FORBIDDEN: 'Access denied.',
    NOT_FOUND: 'The requested resource was not found.',
    VALIDATION_ERROR: 'Please check your input and try again.',
    SESSION_EXPIRED: 'Your session has expired. Please log in again.',
    FILE_TOO_LARGE: 'File size exceeds the maximum allowed limit.',
    INVALID_FILE_TYPE: 'Invalid file type. Please select a supported file.',
    GENERIC_ERROR: 'An unexpected error occurred. Please try again.',
};

// Success Messages
const SUCCESS_MESSAGES = {
    LOGIN_SUCCESS: 'Successfully logged in!',
    LOGOUT_SUCCESS: 'Successfully logged out!',
    REGISTER_SUCCESS: 'Account created successfully!',
    PROFILE_UPDATED: 'Profile updated successfully!',
    PASSWORD_CHANGED: 'Password changed successfully!',
    DATA_SAVED: 'Data saved successfully!',
    DATA_DELETED: 'Data deleted successfully!',
    EMAIL_SENT: 'Email sent successfully!',
    APPOINTMENT_BOOKED: 'Appointment booked successfully!',
    WEIGHT_RECORDED: 'Weight recorded successfully!',
};

// Export configuration objects
if (typeof module !== 'undefined' && module.exports) {
    // Node.js environment
    module.exports = {
        API_CONFIG,
        APP_CONFIG,
        API_ENDPOINTS,
        ERROR_MESSAGES,
        SUCCESS_MESSAGES,
    };
} else {
    // Browser environment
    window.API_CONFIG = API_CONFIG;
    window.APP_CONFIG = APP_CONFIG;
    window.API_ENDPOINTS = API_ENDPOINTS;
    window.ERROR_MESSAGES = ERROR_MESSAGES;
    window.SUCCESS_MESSAGES = SUCCESS_MESSAGES;
}
