#!/usr/bin/env python3
"""
Main Flask application for Preg and Baby Care backend.
Provides RESTful API endpoints for the pregnancy and baby care application.
"""

import os
import logging
from datetime import datetime, timedelta
from flask import Flask, jsonify, request
from flask_cors import CORS
from flask_jwt_extended import J<PERSON><PERSON>anager, create_access_token, jwt_required, get_jwt_identity
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Import route blueprints
from routes.auth import auth_bp
from routes.nutrition import nutrition_bp
from routes.weight import weight_bp
from routes.meal_plans import meal_plans_bp
from routes.chatbot import chatbot_bp
from routes.admin import admin_bp
from routes.appointments import appointments_bp
from routes.baby_care import baby_care_bp

# Import database models
from models.database import init_db, get_db_connection

def create_app():
    """Create and configure the Flask application."""
    app = Flask(__name__)
    
    # Configuration
    app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'dev-secret-key-change-in-production')
    app.config['JWT_SECRET_KEY'] = os.getenv('JWT_SECRET_KEY', 'jwt-secret-key-change-in-production')
    app.config['JWT_ACCESS_TOKEN_EXPIRES'] = timedelta(hours=int(os.getenv('JWT_ACCESS_TOKEN_EXPIRES', 24)))
    app.config['JWT_REFRESH_TOKEN_EXPIRES'] = timedelta(days=int(os.getenv('JWT_REFRESH_TOKEN_EXPIRES', 30)))
    
    # Initialize extensions
    CORS(app, origins=os.getenv('CORS_ORIGINS', 'http://localhost:8000').split(','))
    jwt = JWTManager(app)
    
    # Initialize database
    init_db()
    
    # Register blueprints
    app.register_blueprint(auth_bp, url_prefix='/api/auth')
    app.register_blueprint(nutrition_bp, url_prefix='/api/nutrition')
    app.register_blueprint(weight_bp, url_prefix='/api/weight')
    app.register_blueprint(meal_plans_bp, url_prefix='/api/meal-plans')
    app.register_blueprint(chatbot_bp, url_prefix='/api/chatbot')
    app.register_blueprint(admin_bp, url_prefix='/api/admin')
    app.register_blueprint(appointments_bp, url_prefix='/api/appointments')
    app.register_blueprint(baby_care_bp, url_prefix='/api/baby-care')
    
    # Error handlers
    @app.errorhandler(404)
    def not_found(error):
        return jsonify({'error': 'Resource not found'}), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        return jsonify({'error': 'Internal server error'}), 500
    
    @app.errorhandler(400)
    def bad_request(error):
        return jsonify({'error': 'Bad request'}), 400
    
    @jwt.expired_token_loader
    def expired_token_callback(jwt_header, jwt_payload):
        return jsonify({'error': 'Token has expired'}), 401
    
    @jwt.invalid_token_loader
    def invalid_token_callback(error):
        return jsonify({'error': 'Invalid token'}), 401
    
    @jwt.unauthorized_loader
    def missing_token_callback(error):
        return jsonify({'error': 'Authorization token is required'}), 401
    
    # Health check endpoint
    @app.route('/api/health', methods=['GET'])
    def health_check():
        """Health check endpoint."""
        return jsonify({
            'status': 'healthy',
            'timestamp': datetime.utcnow().isoformat(),
            'version': os.getenv('APP_VERSION', '1.0.0')
        })
    
    # API info endpoint
    @app.route('/api/info', methods=['GET'])
    def api_info():
        """API information endpoint."""
        return jsonify({
            'name': os.getenv('APP_NAME', 'Preg and Baby Care API'),
            'version': os.getenv('APP_VERSION', '1.0.0'),
            'description': 'RESTful API for pregnancy and baby care application',
            'endpoints': {
                'authentication': '/api/auth',
                'nutrition': '/api/nutrition',
                'weight_tracking': '/api/weight',
                'meal_plans': '/api/meal-plans',
                'chatbot': '/api/chatbot',
                'admin': '/api/admin',
                'appointments': '/api/appointments',
                'baby_care': '/api/baby-care'
            },
            'documentation': 'https://github.com/your-repo/preg-baby-care-docs'
        })
    
    # Test database connection
    @app.route('/api/test-db', methods=['GET'])
    def test_db():
        """Test database connection."""
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            cursor.execute('SELECT COUNT(*) FROM users')
            user_count = cursor.fetchone()[0]
            conn.close()
            
            return jsonify({
                'status': 'success',
                'message': 'Database connection successful',
                'user_count': user_count
            })
        except Exception as e:
            return jsonify({
                'status': 'error',
                'message': f'Database connection failed: {str(e)}'
            }), 500
    
    # CORS preflight handler
    @app.before_request
    def handle_preflight():
        if request.method == "OPTIONS":
            response = jsonify({'status': 'ok'})
            response.headers.add("Access-Control-Allow-Origin", "*")
            response.headers.add('Access-Control-Allow-Headers', "*")
            response.headers.add('Access-Control-Allow-Methods', "*")
            return response
    
    # Request logging middleware
    @app.before_request
    def log_request_info():
        if app.debug:
            app.logger.info(f'{request.method} {request.url} - {request.remote_addr}')
    
    # Response headers middleware
    @app.after_request
    def after_request(response):
        response.headers.add('X-Content-Type-Options', 'nosniff')
        response.headers.add('X-Frame-Options', 'DENY')
        response.headers.add('X-XSS-Protection', '1; mode=block')
        return response
    
    return app

def setup_logging():
    """Setup application logging."""
    logging.basicConfig(
        level=logging.INFO if os.getenv('FLASK_ENV') == 'production' else logging.DEBUG,
        format='%(asctime)s %(levelname)s %(name)s %(message)s'
    )

if __name__ == '__main__':
    # Setup logging
    setup_logging()
    
    # Create app
    app = create_app()
    
    # Get configuration
    host = os.getenv('HOST', '127.0.0.1')
    port = int(os.getenv('PORT', 5000))
    debug = os.getenv('FLASK_DEBUG', 'True').lower() == 'true'
    
    print(f"""
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║                    Preg and Baby Care API                    ║
    ║                     Backend Server                           ║
    ║                                                              ║
    ║    🚀 Server starting on http://{host}:{port}                ║
    ║    📚 API Documentation: http://{host}:{port}/api/info      ║
    ║    🏥 Health Check: http://{host}:{port}/api/health         ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """)
    
    # Run the application
    app.run(
        host=host,
        port=port,
        debug=debug,
        threaded=True
    )
