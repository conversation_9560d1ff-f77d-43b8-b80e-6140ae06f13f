#!/usr/bin/env python3
"""
Appointments routes for Preg and Baby Care application.
"""

from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from models.database import get_db_connection
from datetime import datetime

appointments_bp = Blueprint('appointments', __name__)

@appointments_bp.route('/', methods=['GET'])
@jwt_required()
def get_appointments():
    """Get user's appointments."""
    try:
        user_id = get_jwt_identity()
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT * FROM appointments 
            WHERE user_id = ? 
            ORDER BY appointment_date ASC
        ''', (user_id,))
        
        appointments = cursor.fetchall()
        conn.close()
        
        return jsonify({
            'appointments': [dict(appointment) for appointment in appointments]
        }), 200
        
    except Exception as e:
        return jsonify({'error': f'Failed to get appointments: {str(e)}'}), 500

@appointments_bp.route('/', methods=['POST'])
@jwt_required()
def create_appointment():
    """Create a new appointment."""
    try:
        user_id = get_jwt_identity()
        data = request.get_json()
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO appointments (user_id, doctor_name, doctor_specialty,
                                    appointment_date, appointment_type, location, notes)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (
            user_id, data.get('doctor_name'), data.get('doctor_specialty'),
            data.get('appointment_date'), data.get('appointment_type'),
            data.get('location'), data.get('notes')
        ))
        
        appointment_id = cursor.lastrowid
        conn.commit()
        conn.close()
        
        return jsonify({
            'message': 'Appointment created successfully',
            'appointment_id': appointment_id
        }), 201
        
    except Exception as e:
        return jsonify({'error': f'Failed to create appointment: {str(e)}'}), 500
