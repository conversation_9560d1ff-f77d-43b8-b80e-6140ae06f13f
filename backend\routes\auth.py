#!/usr/bin/env python3
"""
Authentication routes for Preg and Baby Care application.
Handles user registration, login, profile management, and JWT token operations.
"""

import re
from datetime import datetime, timedelta
from flask import Blueprint, request, jsonify
from flask_jwt_extended import create_access_token, jwt_required, get_jwt_identity, create_refresh_token
from models.database import get_db_connection, hash_password, verify_password

auth_bp = Blueprint('auth', __name__)

def validate_email(email):
    """Validate email format."""
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def validate_password(password):
    """Validate password strength."""
    if len(password) < 8:
        return False, "Password must be at least 8 characters long"
    if not re.search(r'[A-Z]', password):
        return False, "Password must contain at least one uppercase letter"
    if not re.search(r'[a-z]', password):
        return False, "Password must contain at least one lowercase letter"
    if not re.search(r'\d', password):
        return False, "Password must contain at least one number"
    return True, "Password is valid"

@auth_bp.route('/register', methods=['POST'])
def register():
    """Register a new user."""
    try:
        data = request.get_json()
        
        # Validate required fields
        required_fields = ['email', 'password', 'firstName', 'lastName']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'error': f'{field} is required'}), 400
        
        email = data['email'].lower().strip()
        password = data['password']
        first_name = data['firstName'].strip()
        last_name = data['lastName'].strip()
        phone = data.get('phone', '').strip()
        date_of_birth = data.get('dateOfBirth')
        is_pregnant = data.get('isPregnant', False)
        due_date = data.get('dueDate')
        
        # Validate email
        if not validate_email(email):
            return jsonify({'error': 'Invalid email format'}), 400
        
        # Validate password
        is_valid, message = validate_password(password)
        if not is_valid:
            return jsonify({'error': message}), 400
        
        # Check if user already exists
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT id FROM users WHERE email = ?', (email,))
        if cursor.fetchone():
            conn.close()
            return jsonify({'error': 'User with this email already exists'}), 409
        
        # Hash password
        password_hash = hash_password(password)
        
        # Insert user
        cursor.execute('''
            INSERT INTO users (email, password_hash, first_name, last_name, phone, 
                             date_of_birth, is_pregnant, due_date)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (email, password_hash, first_name, last_name, phone, 
              date_of_birth, is_pregnant, due_date))
        
        user_id = cursor.lastrowid
        
        # Create user profile
        cursor.execute('''
            INSERT INTO user_profiles (user_id) VALUES (?)
        ''', (user_id,))
        
        conn.commit()
        conn.close()
        
        # Create access token
        access_token = create_access_token(identity=user_id)
        refresh_token = create_refresh_token(identity=user_id)
        
        return jsonify({
            'message': 'User registered successfully',
            'access_token': access_token,
            'refresh_token': refresh_token,
            'user': {
                'id': user_id,
                'email': email,
                'firstName': first_name,
                'lastName': last_name,
                'role': 'user',
                'isPregnant': is_pregnant
            }
        }), 201
        
    except Exception as e:
        return jsonify({'error': f'Registration failed: {str(e)}'}), 500

@auth_bp.route('/login', methods=['POST'])
def login():
    """Authenticate user and return JWT token."""
    try:
        data = request.get_json()
        
        if not data.get('email') or not data.get('password'):
            return jsonify({'error': 'Email and password are required'}), 400
        
        email = data['email'].lower().strip()
        password = data['password']
        
        # Find user
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute('''
            SELECT id, email, password_hash, first_name, last_name, role, 
                   is_pregnant, due_date, baby_birth_date, is_active
            FROM users WHERE email = ?
        ''', (email,))
        
        user = cursor.fetchone()
        if not user:
            conn.close()
            return jsonify({'error': 'Invalid email or password'}), 401
        
        # Check if user is active
        if not user['is_active']:
            conn.close()
            return jsonify({'error': 'Account is deactivated'}), 401
        
        # Verify password
        if not verify_password(password, user['password_hash']):
            conn.close()
            return jsonify({'error': 'Invalid email or password'}), 401
        
        # Update last login
        cursor.execute('''
            UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?
        ''', (user['id'],))
        conn.commit()
        conn.close()
        
        # Create tokens
        access_token = create_access_token(identity=user['id'])
        refresh_token = create_refresh_token(identity=user['id'])
        
        return jsonify({
            'message': 'Login successful',
            'access_token': access_token,
            'refresh_token': refresh_token,
            'user': {
                'id': user['id'],
                'email': user['email'],
                'firstName': user['first_name'],
                'lastName': user['last_name'],
                'role': user['role'],
                'isPregnant': bool(user['is_pregnant']),
                'dueDate': user['due_date'],
                'babyBirthDate': user['baby_birth_date']
            }
        }), 200
        
    except Exception as e:
        return jsonify({'error': f'Login failed: {str(e)}'}), 500

@auth_bp.route('/refresh', methods=['POST'])
@jwt_required(refresh=True)
def refresh():
    """Refresh access token."""
    try:
        current_user_id = get_jwt_identity()
        new_token = create_access_token(identity=current_user_id)
        return jsonify({'access_token': new_token}), 200
    except Exception as e:
        return jsonify({'error': f'Token refresh failed: {str(e)}'}), 500

@auth_bp.route('/profile', methods=['GET'])
@jwt_required()
def get_profile():
    """Get user profile information."""
    try:
        user_id = get_jwt_identity()
        
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute('''
            SELECT u.*, p.height_cm, p.pre_pregnancy_weight_kg, p.blood_type,
                   p.allergies, p.medical_conditions, p.emergency_contact_name,
                   p.emergency_contact_phone, p.doctor_name, p.doctor_phone,
                   p.hospital_preference
            FROM users u
            LEFT JOIN user_profiles p ON u.id = p.user_id
            WHERE u.id = ?
        ''', (user_id,))
        
        user = cursor.fetchone()
        conn.close()
        
        if not user:
            return jsonify({'error': 'User not found'}), 404
        
        return jsonify({
            'user': {
                'id': user['id'],
                'email': user['email'],
                'firstName': user['first_name'],
                'lastName': user['last_name'],
                'phone': user['phone'],
                'dateOfBirth': user['date_of_birth'],
                'role': user['role'],
                'isPregnant': bool(user['is_pregnant']),
                'dueDate': user['due_date'],
                'babyBirthDate': user['baby_birth_date'],
                'heightCm': user['height_cm'],
                'prePregnancyWeightKg': user['pre_pregnancy_weight_kg'],
                'bloodType': user['blood_type'],
                'allergies': user['allergies'],
                'medicalConditions': user['medical_conditions'],
                'emergencyContactName': user['emergency_contact_name'],
                'emergencyContactPhone': user['emergency_contact_phone'],
                'doctorName': user['doctor_name'],
                'doctorPhone': user['doctor_phone'],
                'hospitalPreference': user['hospital_preference'],
                'createdAt': user['created_at'],
                'lastLogin': user['last_login']
            }
        }), 200
        
    except Exception as e:
        return jsonify({'error': f'Failed to get profile: {str(e)}'}), 500

@auth_bp.route('/profile', methods=['PUT'])
@jwt_required()
def update_profile():
    """Update user profile information."""
    try:
        user_id = get_jwt_identity()
        data = request.get_json()
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Update user table
        user_fields = ['first_name', 'last_name', 'phone', 'date_of_birth', 
                      'is_pregnant', 'due_date', 'baby_birth_date']
        user_updates = []
        user_values = []
        
        field_mapping = {
            'firstName': 'first_name',
            'lastName': 'last_name',
            'dateOfBirth': 'date_of_birth',
            'isPregnant': 'is_pregnant',
            'dueDate': 'due_date',
            'babyBirthDate': 'baby_birth_date'
        }
        
        for key, value in data.items():
            db_field = field_mapping.get(key, key)
            if db_field in user_fields and value is not None:
                user_updates.append(f"{db_field} = ?")
                user_values.append(value)
        
        if user_updates:
            user_values.append(user_id)
            cursor.execute(f'''
                UPDATE users SET {', '.join(user_updates)}, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', user_values)
        
        # Update profile table
        profile_fields = ['height_cm', 'pre_pregnancy_weight_kg', 'blood_type',
                         'allergies', 'medical_conditions', 'emergency_contact_name',
                         'emergency_contact_phone', 'doctor_name', 'doctor_phone',
                         'hospital_preference']
        profile_updates = []
        profile_values = []
        
        profile_mapping = {
            'heightCm': 'height_cm',
            'prePregnancyWeightKg': 'pre_pregnancy_weight_kg',
            'bloodType': 'blood_type',
            'medicalConditions': 'medical_conditions',
            'emergencyContactName': 'emergency_contact_name',
            'emergencyContactPhone': 'emergency_contact_phone',
            'doctorName': 'doctor_name',
            'doctorPhone': 'doctor_phone',
            'hospitalPreference': 'hospital_preference'
        }
        
        for key, value in data.items():
            db_field = profile_mapping.get(key, key)
            if db_field in profile_fields and value is not None:
                profile_updates.append(f"{db_field} = ?")
                profile_values.append(value)
        
        if profile_updates:
            profile_values.append(user_id)
            cursor.execute(f'''
                UPDATE user_profiles SET {', '.join(profile_updates)}, updated_at = CURRENT_TIMESTAMP
                WHERE user_id = ?
            ''', profile_values)
        
        conn.commit()
        conn.close()
        
        return jsonify({'message': 'Profile updated successfully'}), 200
        
    except Exception as e:
        return jsonify({'error': f'Failed to update profile: {str(e)}'}), 500

@auth_bp.route('/change-password', methods=['POST'])
@jwt_required()
def change_password():
    """Change user password."""
    try:
        user_id = get_jwt_identity()
        data = request.get_json()
        
        current_password = data.get('currentPassword')
        new_password = data.get('newPassword')
        
        if not current_password or not new_password:
            return jsonify({'error': 'Current password and new password are required'}), 400
        
        # Validate new password
        is_valid, message = validate_password(new_password)
        if not is_valid:
            return jsonify({'error': message}), 400
        
        # Verify current password
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT password_hash FROM users WHERE id = ?', (user_id,))
        user = cursor.fetchone()
        
        if not user or not verify_password(current_password, user['password_hash']):
            conn.close()
            return jsonify({'error': 'Current password is incorrect'}), 401
        
        # Update password
        new_password_hash = hash_password(new_password)
        cursor.execute('''
            UPDATE users SET password_hash = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        ''', (new_password_hash, user_id))
        
        conn.commit()
        conn.close()
        
        return jsonify({'message': 'Password changed successfully'}), 200
        
    except Exception as e:
        return jsonify({'error': f'Failed to change password: {str(e)}'}), 500
