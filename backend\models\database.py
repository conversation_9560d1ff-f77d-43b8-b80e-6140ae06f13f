#!/usr/bin/env python3
"""
Database models and initialization for Preg and Baby Care application.
Uses SQLite for easy deployment and development.
"""

import sqlite3
import os
import hashlib
import secrets
from datetime import datetime, timedelta
from typing import Optional, Dict, List, Any

# Database file path
DB_PATH = os.path.join(os.path.dirname(__file__), '..', 'database', 'preg_baby_care.db')

def get_db_connection():
    """Get a database connection with row factory."""
    # Ensure database directory exists
    os.makedirs(os.path.dirname(DB_PATH), exist_ok=True)
    
    conn = sqlite3.connect(DB_PATH)
    conn.row_factory = sqlite3.Row  # Enable column access by name
    return conn

def hash_password(password: str) -> str:
    """Hash a password using SHA-256 with salt."""
    salt = secrets.token_hex(16)
    password_hash = hashlib.sha256((password + salt).encode()).hexdigest()
    return f"{salt}:{password_hash}"

def verify_password(password: str, hashed: str) -> bool:
    """Verify a password against its hash."""
    try:
        salt, password_hash = hashed.split(':')
        return hashlib.sha256((password + salt).encode()).hexdigest() == password_hash
    except ValueError:
        return False

def init_db():
    """Initialize the database with all required tables."""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    # Users table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            email TEXT UNIQUE NOT NULL,
            password_hash TEXT NOT NULL,
            first_name TEXT NOT NULL,
            last_name TEXT NOT NULL,
            phone TEXT,
            date_of_birth DATE,
            role TEXT DEFAULT 'user' CHECK (role IN ('user', 'admin')),
            is_pregnant BOOLEAN DEFAULT FALSE,
            due_date DATE,
            baby_birth_date DATE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_login TIMESTAMP,
            is_active BOOLEAN DEFAULT TRUE
        )
    ''')
    
    # User profiles table for additional information
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS user_profiles (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            height_cm REAL,
            pre_pregnancy_weight_kg REAL,
            blood_type TEXT,
            allergies TEXT,
            medical_conditions TEXT,
            emergency_contact_name TEXT,
            emergency_contact_phone TEXT,
            doctor_name TEXT,
            doctor_phone TEXT,
            hospital_preference TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
        )
    ''')
    
    # Weight tracking table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS weight_tracking (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            weight_kg REAL NOT NULL,
            week_of_pregnancy INTEGER,
            baby_age_weeks INTEGER,
            notes TEXT,
            recorded_date DATE NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
        )
    ''')
    
    # Nutrition plans table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS nutrition_plans (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title TEXT NOT NULL,
            description TEXT,
            trimester INTEGER CHECK (trimester IN (1, 2, 3)),
            baby_age_months INTEGER,
            target_audience TEXT CHECK (target_audience IN ('pregnancy', 'baby', 'both')),
            calories_per_day INTEGER,
            protein_grams INTEGER,
            carbs_grams INTEGER,
            fat_grams INTEGER,
            fiber_grams INTEGER,
            iron_mg REAL,
            calcium_mg REAL,
            folic_acid_mcg REAL,
            is_active BOOLEAN DEFAULT TRUE,
            created_by INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (created_by) REFERENCES users (id)
        )
    ''')
    
    # Daily meal plans table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS daily_meal_plans (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            nutrition_plan_id INTEGER,
            day_of_week INTEGER CHECK (day_of_week BETWEEN 1 AND 7),
            week_number INTEGER DEFAULT 1,
            trimester INTEGER CHECK (trimester IN (1, 2, 3)),
            meal_type TEXT CHECK (meal_type IN ('breakfast', 'lunch', 'dinner', 'snack')),
            main_dish TEXT NOT NULL,
            sides TEXT,
            drinks TEXT,
            nutrition_highlights TEXT,
            calories INTEGER,
            preparation_time_minutes INTEGER,
            difficulty_level TEXT CHECK (difficulty_level IN ('easy', 'medium', 'hard')),
            ingredients TEXT,
            instructions TEXT,
            tips TEXT,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (nutrition_plan_id) REFERENCES nutrition_plans (id) ON DELETE CASCADE
        )
    ''')
    
    # Exercise plans table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS exercise_plans (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title TEXT NOT NULL,
            description TEXT,
            trimester INTEGER CHECK (trimester IN (1, 2, 3)),
            difficulty_level TEXT CHECK (difficulty_level IN ('beginner', 'intermediate', 'advanced')),
            duration_minutes INTEGER,
            target_area TEXT,
            equipment_needed TEXT,
            instructions TEXT,
            precautions TEXT,
            benefits TEXT,
            video_url TEXT,
            image_url TEXT,
            is_active BOOLEAN DEFAULT TRUE,
            created_by INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (created_by) REFERENCES users (id)
        )
    ''')

    # Appointments table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS appointments (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            doctor_name TEXT NOT NULL,
            doctor_specialty TEXT,
            appointment_date DATETIME NOT NULL,
            appointment_type TEXT CHECK (appointment_type IN ('checkup', 'ultrasound', 'consultation', 'emergency')),
            location TEXT,
            notes TEXT,
            status TEXT DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'completed', 'cancelled', 'rescheduled')),
            reminder_sent BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
        )
    ''')

    # Baby care information table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS baby_care_info (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title TEXT NOT NULL,
            category TEXT CHECK (category IN ('feeding', 'sleep', 'development', 'health', 'activities')),
            age_range_start_months INTEGER,
            age_range_end_months INTEGER,
            content TEXT NOT NULL,
            tips TEXT,
            warning_signs TEXT,
            when_to_call_doctor TEXT,
            is_active BOOLEAN DEFAULT TRUE,
            created_by INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (created_by) REFERENCES users (id)
        )
    ''')

    # Sleep patterns table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS sleep_patterns (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            baby_age_months INTEGER,
            sleep_start_time TIME,
            sleep_end_time TIME,
            nap_times TEXT,
            total_sleep_hours REAL,
            sleep_quality TEXT CHECK (sleep_quality IN ('excellent', 'good', 'fair', 'poor')),
            notes TEXT,
            recorded_date DATE NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
        )
    ''')

    # Vaccination records table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS vaccination_records (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            vaccine_name TEXT NOT NULL,
            vaccine_type TEXT,
            administered_date DATE NOT NULL,
            next_due_date DATE,
            doctor_name TEXT,
            location TEXT,
            batch_number TEXT,
            side_effects TEXT,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
        )
    ''')

    # Chat history table for AI conversations
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS chat_history (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER,
            session_id TEXT NOT NULL,
            message_type TEXT CHECK (message_type IN ('user', 'assistant')),
            message_content TEXT NOT NULL,
            context_data TEXT,
            sentiment_score REAL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
        )
    ''')

    # Government schemes table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS government_schemes (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            scheme_name TEXT NOT NULL,
            description TEXT,
            eligibility_criteria TEXT,
            benefits TEXT,
            application_process TEXT,
            required_documents TEXT,
            contact_information TEXT,
            website_url TEXT,
            is_active BOOLEAN DEFAULT TRUE,
            created_by INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (created_by) REFERENCES users (id)
        )
    ''')

    conn.commit()
    conn.close()
    print("✅ Database initialized successfully!")

def create_sample_admin():
    """Create a sample admin user for testing."""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    # Check if admin already exists
    cursor.execute('SELECT id FROM users WHERE email = ?', ('<EMAIL>',))
    if cursor.fetchone():
        conn.close()
        return
    
    # Create admin user
    admin_password = hash_password('admin123')
    cursor.execute('''
        INSERT INTO users (email, password_hash, first_name, last_name, role, is_active)
        VALUES (?, ?, ?, ?, ?, ?)
    ''', ('<EMAIL>', admin_password, 'Admin', 'User', 'admin', True))
    
    admin_id = cursor.lastrowid
    
    # Create admin profile
    cursor.execute('''
        INSERT INTO user_profiles (user_id, height_cm, emergency_contact_name, emergency_contact_phone)
        VALUES (?, ?, ?, ?)
    ''', (admin_id, 165.0, 'Emergency Contact', '+1234567890'))
    
    conn.commit()
    conn.close()
    print("✅ Sample admin user created: <EMAIL> / admin123")

if __name__ == '__main__':
    init_db()
    create_sample_admin()
