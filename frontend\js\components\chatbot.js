/**
 * Chatbot component for Preg and Baby Care application
 * Integrates with Gemini AI API for intelligent responses
 */

class ChatbotManager {
    constructor() {
        this.isOpen = false;
        this.isTyping = false;
        this.sessionId = null;
        this.messageHistory = [];
        this.container = null;
        this.chatMessages = null;
        this.chatInput = null;
        this.sendButton = null;
        
        this.init();
    }

    /**
     * Initialize chatbot
     */
    init() {
        this.createChatbotHTML();
        this.setupEventListeners();
        this.loadChatHistory();
        this.initializeSession();
    }

    /**
     * Create chatbot HTML structure
     */
    createChatbotHTML() {
        const container = document.getElementById('chatbot-container');
        if (!container) return;

        container.innerHTML = `
            <div class="chatbot" id="chatbot">
                <div class="chatbot-header">
                    <div class="chatbot-title">
                        <i class="fas fa-robot"></i>
                        <span>AI Assistant</span>
                    </div>
                    <div class="chatbot-status" id="chatbot-status">
                        <span class="status-indicator online"></span>
                        <span class="status-text">Online</span>
                    </div>
                    <button class="chatbot-close" id="chatbot-close">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <div class="chatbot-messages" id="chatbot-messages">
                    <div class="welcome-message">
                        <div class="message bot-message">
                            <div class="message-avatar">
                                <i class="fas fa-robot"></i>
                            </div>
                            <div class="message-content">
                                <p>Hello! I'm your AI assistant for pregnancy and baby care. How can I help you today?</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="chatbot-input-container">
                    <div class="chatbot-input-wrapper">
                        <textarea 
                            id="chatbot-input" 
                            class="chatbot-input" 
                            placeholder="Ask me anything about pregnancy or baby care..."
                            rows="1"
                        ></textarea>
                        <button id="chatbot-send" class="chatbot-send" disabled>
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                    <div class="chatbot-suggestions" id="chatbot-suggestions">
                        <!-- Suggestions will be populated here -->
                    </div>
                </div>
            </div>
            
            <button class="chatbot-toggle" id="chatbot-toggle">
                <i class="fas fa-comments"></i>
                <span class="chatbot-badge" id="chatbot-badge" style="display: none;">1</span>
            </button>
        `;

        this.container = document.getElementById('chatbot');
        this.chatMessages = document.getElementById('chatbot-messages');
        this.chatInput = document.getElementById('chatbot-input');
        this.sendButton = document.getElementById('chatbot-send');
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        const toggleButton = document.getElementById('chatbot-toggle');
        const closeButton = document.getElementById('chatbot-close');

        // Toggle chatbot
        if (toggleButton) {
            toggleButton.addEventListener('click', () => {
                this.toggle();
            });
        }

        // Close chatbot
        if (closeButton) {
            closeButton.addEventListener('click', () => {
                this.close();
            });
        }

        // Send message
        if (this.sendButton) {
            this.sendButton.addEventListener('click', () => {
                this.sendMessage();
            });
        }

        // Input handling
        if (this.chatInput) {
            this.chatInput.addEventListener('input', () => {
                this.handleInputChange();
                this.autoResize();
            });

            this.chatInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    this.sendMessage();
                }
            });
        }

        // Close on escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isOpen) {
                this.close();
            }
        });
    }

    /**
     * Toggle chatbot visibility
     */
    toggle() {
        if (this.isOpen) {
            this.close();
        } else {
            this.open();
        }
    }

    /**
     * Open chatbot
     */
    open() {
        this.isOpen = true;
        this.container.classList.add('open');
        this.chatInput.focus();
        this.hideBadge();
        this.loadSuggestions();
    }

    /**
     * Close chatbot
     */
    close() {
        this.isOpen = false;
        this.container.classList.remove('open');
    }

    /**
     * Handle input change
     */
    handleInputChange() {
        const hasText = this.chatInput.value.trim().length > 0;
        this.sendButton.disabled = !hasText;
        this.sendButton.classList.toggle('active', hasText);
    }

    /**
     * Auto-resize textarea
     */
    autoResize() {
        this.chatInput.style.height = 'auto';
        this.chatInput.style.height = Math.min(this.chatInput.scrollHeight, 120) + 'px';
    }

    /**
     * Send message
     */
    async sendMessage() {
        const message = this.chatInput.value.trim();
        if (!message || this.isTyping) return;

        // Add user message to chat
        this.addMessage(message, 'user');
        this.chatInput.value = '';
        this.handleInputChange();
        this.autoResize();

        // Show typing indicator
        this.showTyping();

        try {
            // Send message to API
            const response = await ApiService.chatbot.sendMessage({
                message: message,
                session_id: this.sessionId
            });

            this.hideTyping();

            if (response.success) {
                // Add bot response
                this.addMessage(response.data.response, 'bot');
                
                // Update session ID if provided
                if (response.data.session_id) {
                    this.sessionId = response.data.session_id;
                }

                // Load new suggestions if provided
                if (response.data.suggestions) {
                    this.loadSuggestions(response.data.suggestions);
                }
            } else {
                this.addMessage('Sorry, I encountered an error. Please try again.', 'bot', 'error');
            }
        } catch (error) {
            console.error('Chatbot error:', error);
            this.hideTyping();
            this.addMessage('Sorry, I\'m having trouble connecting. Please try again later.', 'bot', 'error');
        }
    }

    /**
     * Add message to chat
     */
    addMessage(content, sender, type = 'normal') {
        const messageElement = document.createElement('div');
        messageElement.className = `message ${sender}-message ${type}`;
        
        const timestamp = new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
        
        messageElement.innerHTML = `
            <div class="message-avatar">
                <i class="fas fa-${sender === 'user' ? 'user' : 'robot'}"></i>
            </div>
            <div class="message-content">
                <p>${this.formatMessage(content)}</p>
                <span class="message-time">${timestamp}</span>
            </div>
        `;

        this.chatMessages.appendChild(messageElement);
        this.scrollToBottom();

        // Store in history
        this.messageHistory.push({
            content,
            sender,
            timestamp: new Date().toISOString()
        });

        // Save to localStorage
        this.saveChatHistory();
    }

    /**
     * Format message content
     */
    formatMessage(content) {
        // Convert URLs to links
        content = content.replace(
            /(https?:\/\/[^\s]+)/g,
            '<a href="$1" target="_blank" rel="noopener noreferrer">$1</a>'
        );

        // Convert line breaks to <br>
        content = content.replace(/\n/g, '<br>');

        return content;
    }

    /**
     * Show typing indicator
     */
    showTyping() {
        if (this.isTyping) return;
        
        this.isTyping = true;
        const typingElement = document.createElement('div');
        typingElement.className = 'message bot-message typing';
        typingElement.id = 'typing-indicator';
        
        typingElement.innerHTML = `
            <div class="message-avatar">
                <i class="fas fa-robot"></i>
            </div>
            <div class="message-content">
                <div class="typing-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        `;

        this.chatMessages.appendChild(typingElement);
        this.scrollToBottom();
    }

    /**
     * Hide typing indicator
     */
    hideTyping() {
        this.isTyping = false;
        const typingElement = document.getElementById('typing-indicator');
        if (typingElement) {
            typingElement.remove();
        }
    }

    /**
     * Scroll to bottom of chat
     */
    scrollToBottom() {
        this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
    }

    /**
     * Load chat suggestions
     */
    loadSuggestions(suggestions = null) {
        const suggestionsContainer = document.getElementById('chatbot-suggestions');
        if (!suggestionsContainer) return;

        // Default suggestions if none provided
        if (!suggestions) {
            suggestions = [
                "What should I eat during pregnancy?",
                "When should my baby start solid foods?",
                "How much weight should I gain?",
                "What are the signs of labor?"
            ];
        }

        suggestionsContainer.innerHTML = suggestions.map(suggestion => `
            <button class="suggestion-btn" onclick="chatbotManager.sendSuggestion('${suggestion}')">
                ${suggestion}
            </button>
        `).join('');
    }

    /**
     * Send suggestion as message
     */
    sendSuggestion(suggestion) {
        this.chatInput.value = suggestion;
        this.handleInputChange();
        this.sendMessage();
    }

    /**
     * Initialize chat session
     */
    async initializeSession() {
        try {
            const response = await ApiService.chatbot.createSession();
            if (response.success) {
                this.sessionId = response.data.session_id;
            }
        } catch (error) {
            console.error('Failed to initialize chat session:', error);
        }
    }

    /**
     * Load chat history from localStorage
     */
    loadChatHistory() {
        try {
            const history = localStorage.getItem(CONFIG.STORAGE_KEYS.CHAT_HISTORY);
            if (history) {
                this.messageHistory = JSON.parse(history);
                
                // Restore recent messages (last 10)
                const recentMessages = this.messageHistory.slice(-10);
                recentMessages.forEach(msg => {
                    if (msg.sender !== 'system') {
                        this.addMessageToDOM(msg.content, msg.sender, msg.timestamp);
                    }
                });
            }
        } catch (error) {
            console.error('Error loading chat history:', error);
        }
    }

    /**
     * Save chat history to localStorage
     */
    saveChatHistory() {
        try {
            // Keep only last 50 messages
            const historyToSave = this.messageHistory.slice(-50);
            localStorage.setItem(CONFIG.STORAGE_KEYS.CHAT_HISTORY, JSON.stringify(historyToSave));
        } catch (error) {
            console.error('Error saving chat history:', error);
        }
    }

    /**
     * Add message to DOM without storing in history
     */
    addMessageToDOM(content, sender, timestamp) {
        const messageElement = document.createElement('div');
        messageElement.className = `message ${sender}-message`;
        
        const time = new Date(timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
        
        messageElement.innerHTML = `
            <div class="message-avatar">
                <i class="fas fa-${sender === 'user' ? 'user' : 'robot'}"></i>
            </div>
            <div class="message-content">
                <p>${this.formatMessage(content)}</p>
                <span class="message-time">${time}</span>
            </div>
        `;

        // Insert before welcome message
        const welcomeMessage = this.chatMessages.querySelector('.welcome-message');
        if (welcomeMessage) {
            this.chatMessages.insertBefore(messageElement, welcomeMessage.nextSibling);
        } else {
            this.chatMessages.appendChild(messageElement);
        }
    }

    /**
     * Clear chat history
     */
    clearHistory() {
        this.messageHistory = [];
        localStorage.removeItem(CONFIG.STORAGE_KEYS.CHAT_HISTORY);
        
        // Clear messages except welcome
        const messages = this.chatMessages.querySelectorAll('.message:not(.welcome-message .message)');
        messages.forEach(msg => msg.remove());
    }

    /**
     * Show notification badge
     */
    showBadge(count = 1) {
        const badge = document.getElementById('chatbot-badge');
        if (badge) {
            badge.textContent = count;
            badge.style.display = 'block';
        }
    }

    /**
     * Hide notification badge
     */
    hideBadge() {
        const badge = document.getElementById('chatbot-badge');
        if (badge) {
            badge.style.display = 'none';
        }
    }

    /**
     * Update chatbot status
     */
    updateStatus(status, text) {
        const statusIndicator = document.querySelector('.status-indicator');
        const statusText = document.querySelector('.status-text');
        
        if (statusIndicator) {
            statusIndicator.className = `status-indicator ${status}`;
        }
        
        if (statusText) {
            statusText.textContent = text;
        }
    }

    /**
     * Handle connection status
     */
    handleConnectionStatus(isOnline) {
        if (isOnline) {
            this.updateStatus('online', 'Online');
        } else {
            this.updateStatus('offline', 'Offline');
        }
    }
}

// Global function to open chatbot (called from other components)
function openChatbot() {
    if (window.chatbotManager) {
        window.chatbotManager.open();
    }
}

// Initialize chatbot when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.chatbotManager = new ChatbotManager();
    
    // Handle online/offline status
    window.addEventListener('online', () => {
        window.chatbotManager.handleConnectionStatus(true);
    });
    
    window.addEventListener('offline', () => {
        window.chatbotManager.handleConnectionStatus(false);
    });
});

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ChatbotManager;
}
