#!/usr/bin/env python3
"""
Weight tracking routes for Preg and Baby Care application.
"""

from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from models.database import get_db_connection
from datetime import datetime, timedelta

weight_bp = Blueprint('weight', __name__)

@weight_bp.route('/records', methods=['GET'])
@jwt_required()
def get_weight_records():
    """Get user's weight tracking records."""
    try:
        user_id = get_jwt_identity()
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT * FROM weight_tracking 
            WHERE user_id = ? 
            ORDER BY recorded_date DESC
        ''', (user_id,))
        
        records = cursor.fetchall()
        conn.close()
        
        return jsonify({
            'records': [dict(record) for record in records]
        }), 200
        
    except Exception as e:
        return jsonify({'error': f'Failed to get weight records: {str(e)}'}), 500

@weight_bp.route('/records', methods=['POST'])
@jwt_required()
def add_weight_record():
    """Add a new weight record."""
    try:
        user_id = get_jwt_identity()
        data = request.get_json()
        
        weight_kg = data.get('weight_kg')
        week_of_pregnancy = data.get('week_of_pregnancy')
        baby_age_weeks = data.get('baby_age_weeks')
        notes = data.get('notes', '')
        recorded_date = data.get('recorded_date', datetime.now().strftime('%Y-%m-%d'))
        
        if not weight_kg:
            return jsonify({'error': 'Weight is required'}), 400
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO weight_tracking (user_id, weight_kg, week_of_pregnancy, 
                                       baby_age_weeks, notes, recorded_date)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (user_id, weight_kg, week_of_pregnancy, baby_age_weeks, notes, recorded_date))
        
        record_id = cursor.lastrowid
        conn.commit()
        conn.close()
        
        return jsonify({
            'message': 'Weight record added successfully',
            'record_id': record_id
        }), 201
        
    except Exception as e:
        return jsonify({'error': f'Failed to add weight record: {str(e)}'}), 500

@weight_bp.route('/expected', methods=['GET'])
@jwt_required()
def get_expected_weight():
    """Get expected weight gain information."""
    try:
        user_id = get_jwt_identity()
        week = request.args.get('week', type=int)
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Get user's pre-pregnancy weight and height
        cursor.execute('''
            SELECT p.pre_pregnancy_weight_kg, p.height_cm, u.is_pregnant
            FROM users u
            LEFT JOIN user_profiles p ON u.id = p.user_id
            WHERE u.id = ?
        ''', (user_id,))
        
        user = cursor.fetchone()
        conn.close()
        
        if not user or not user['is_pregnant']:
            return jsonify({'error': 'User not found or not pregnant'}), 404
        
        # Calculate BMI if height and weight available
        bmi_category = 'normal'
        if user['height_cm'] and user['pre_pregnancy_weight_kg']:
            height_m = user['height_cm'] / 100
            bmi = user['pre_pregnancy_weight_kg'] / (height_m ** 2)
            
            if bmi < 18.5:
                bmi_category = 'underweight'
            elif bmi >= 25 and bmi < 30:
                bmi_category = 'overweight'
            elif bmi >= 30:
                bmi_category = 'obese'
        
        # Weight gain recommendations by BMI category
        weight_gain_ranges = {
            'underweight': {'min': 12.5, 'max': 18, 'weekly_second_third': 0.5},
            'normal': {'min': 11.5, 'max': 16, 'weekly_second_third': 0.4},
            'overweight': {'min': 7, 'max': 11.5, 'weekly_second_third': 0.3},
            'obese': {'min': 5, 'max': 9, 'weekly_second_third': 0.2}
        }
        
        range_info = weight_gain_ranges[bmi_category]
        
        # Calculate expected weight for specific week
        expected_weight = None
        if week and user['pre_pregnancy_weight_kg']:
            if week <= 12:  # First trimester
                expected_gain = range_info['min'] * 0.1  # 10% of total gain in first trimester
            else:  # Second and third trimester
                first_trimester_gain = range_info['min'] * 0.1
                remaining_weeks = week - 12
                additional_gain = remaining_weeks * range_info['weekly_second_third']
                expected_gain = first_trimester_gain + additional_gain
            
            expected_weight = user['pre_pregnancy_weight_kg'] + expected_gain
        
        return jsonify({
            'bmi_category': bmi_category,
            'total_weight_gain_range': range_info,
            'expected_weight_for_week': expected_weight,
            'week': week,
            'recommendations': [
                'Weight gain varies for each individual',
                'Consult your healthcare provider for personalized advice',
                'Focus on healthy eating rather than just weight numbers'
            ]
        }), 200
        
    except Exception as e:
        return jsonify({'error': f'Failed to get expected weight: {str(e)}'}), 500
