<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Appointments - Preg and Baby Care</title>
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="../css/components.css">
    <link rel="stylesheet" href="../css/responsive.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar" id="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <a href="../index.html" class="brand-link">
                    <i class="fas fa-baby brand-icon"></i>
                    <span class="brand-text">Preg and Baby Care</span>
                </a>
            </div>
            
            <div class="nav-menu" id="nav-menu">
                <div class="nav-section">
                    <div class="nav-dropdown">
                        <a href="#" class="nav-link dropdown-toggle">
                            <i class="fas fa-pregnant"></i>
                            Pregnancy Care
                            <i class="fas fa-chevron-down"></i>
                        </a>
                        <div class="dropdown-menu">
                            <a href="pregnancy-care.html" class="dropdown-item">
                                <i class="fas fa-heart"></i>
                                Pregnancy Guide
                            </a>
                            <a href="nutrition.html" class="dropdown-item">
                                <i class="fas fa-apple-alt"></i>
                                Nutrition Plans
                            </a>
                            <a href="weight-tracker.html" class="dropdown-item">
                                <i class="fas fa-weight"></i>
                                Weight Tracker
                            </a>
                            <a href="exercise.html" class="dropdown-item">
                                <i class="fas fa-dumbbell"></i>
                                Exercise Guide
                            </a>
                        </div>
                    </div>
                    
                    <div class="nav-dropdown">
                        <a href="#" class="nav-link dropdown-toggle">
                            <i class="fas fa-baby"></i>
                            Baby Care
                            <i class="fas fa-chevron-down"></i>
                        </a>
                        <div class="dropdown-menu">
                            <a href="baby-care.html" class="dropdown-item">
                                <i class="fas fa-baby-carriage"></i>
                                Baby Guide
                            </a>
                            <a href="baby-nutrition.html" class="dropdown-item">
                                <i class="fas fa-baby"></i>
                                Baby Nutrition
                            </a>
                            <a href="sleep-patterns.html" class="dropdown-item">
                                <i class="fas fa-moon"></i>
                                Sleep Patterns
                            </a>
                            <a href="vaccinations.html" class="dropdown-item">
                                <i class="fas fa-syringe"></i>
                                Vaccinations
                            </a>
                        </div>
                    </div>
                    
                    <a href="appointments.html" class="nav-link">
                        <i class="fas fa-calendar-check"></i>
                        Appointments
                    </a>
                    
                    <a href="meal-plans.html" class="nav-link">
                        <i class="fas fa-utensils"></i>
                        Meal Plans
                    </a>
                </div>
                
                <div class="nav-auth" id="nav-auth">
                    <!-- Will be populated by JavaScript based on auth state -->
                </div>
            </div>
            
            <div class="nav-toggle" id="nav-toggle">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Hero Section -->
        <section class="page-hero">
            <div class="container">
                <div class="hero-content">
                    <h1 class="hero-title">
                        <i class="fas fa-calendar-check"></i>
                        My Appointments
                    </h1>
                    <p class="hero-description">
                        Schedule and manage your prenatal appointments and baby care visits with healthcare providers.
                    </p>
                </div>
            </div>
        </section>

        <!-- Quick Actions -->
        <section class="appointment-actions">
            <div class="container">
                <div class="actions-row">
                    <button class="btn btn-primary" onclick="openBookingModal()">
                        <i class="fas fa-plus"></i>
                        Book New Appointment
                    </button>
                    
                    <div class="view-controls">
                        <button class="btn btn-outline btn-sm active" data-view="upcoming" onclick="switchView('upcoming')">
                            <i class="fas fa-clock"></i>
                            Upcoming
                        </button>
                        <button class="btn btn-outline btn-sm" data-view="past" onclick="switchView('past')">
                            <i class="fas fa-history"></i>
                            Past
                        </button>
                        <button class="btn btn-outline btn-sm" data-view="all" onclick="switchView('all')">
                            <i class="fas fa-list"></i>
                            All
                        </button>
                    </div>
                </div>
            </div>
        </section>

        <!-- Upcoming Appointments -->
        <section class="upcoming-appointments">
            <div class="container">
                <div class="appointments-header">
                    <h2 class="section-title">Upcoming Appointments</h2>
                    <div class="appointments-summary" id="appointments-summary">
                        <span class="summary-item">
                            <i class="fas fa-calendar"></i>
                            <span id="total-upcoming">0</span> upcoming
                        </span>
                        <span class="summary-item">
                            <i class="fas fa-clock"></i>
                            Next: <span id="next-appointment">--</span>
                        </span>
                    </div>
                </div>
                
                <div class="appointments-grid" id="upcoming-appointments-grid">
                    <!-- Upcoming appointments will be populated here -->
                </div>
                
                <div class="empty-state" id="upcoming-empty-state" style="display: none;">
                    <i class="fas fa-calendar-plus"></i>
                    <h4>No Upcoming Appointments</h4>
                    <p>You don't have any scheduled appointments. Book your next visit with a healthcare provider.</p>
                    <button class="btn btn-primary" onclick="openBookingModal()">
                        <i class="fas fa-plus"></i>
                        Book Appointment
                    </button>
                </div>
            </div>
        </section>

        <!-- Past Appointments -->
        <section class="past-appointments" id="past-appointments-section" style="display: none;">
            <div class="container">
                <h2 class="section-title">Past Appointments</h2>
                
                <div class="appointments-grid" id="past-appointments-grid">
                    <!-- Past appointments will be populated here -->
                </div>
                
                <div class="empty-state" id="past-empty-state" style="display: none;">
                    <i class="fas fa-history"></i>
                    <h4>No Past Appointments</h4>
                    <p>Your appointment history will appear here after you've had visits with healthcare providers.</p>
                </div>
            </div>
        </section>

        <!-- Appointment Reminders -->
        <section class="appointment-reminders">
            <div class="container">
                <div class="reminders-card">
                    <div class="reminders-header">
                        <h3><i class="fas fa-bell"></i> Appointment Reminders</h3>
                        <div class="reminder-settings">
                            <label class="toggle-switch">
                                <input type="checkbox" id="email-reminders" checked>
                                <span class="toggle-slider"></span>
                                Email Reminders
                            </label>
                            <label class="toggle-switch">
                                <input type="checkbox" id="sms-reminders">
                                <span class="toggle-slider"></span>
                                SMS Reminders
                            </label>
                        </div>
                    </div>
                    <div class="reminders-content">
                        <div class="reminder-options">
                            <div class="reminder-option">
                                <label>
                                    <input type="checkbox" checked>
                                    24 hours before appointment
                                </label>
                            </div>
                            <div class="reminder-option">
                                <label>
                                    <input type="checkbox" checked>
                                    2 hours before appointment
                                </label>
                            </div>
                            <div class="reminder-option">
                                <label>
                                    <input type="checkbox">
                                    1 week before appointment
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Recommended Appointments -->
        <section class="recommended-appointments">
            <div class="container">
                <h2 class="section-title">Recommended Appointment Schedule</h2>
                
                <div class="schedule-tabs">
                    <button class="tab-btn active" data-schedule="pregnancy">
                        <i class="fas fa-pregnant"></i>
                        Pregnancy Schedule
                    </button>
                    <button class="tab-btn" data-schedule="baby">
                        <i class="fas fa-baby"></i>
                        Baby Schedule
                    </button>
                </div>

                <div class="schedule-content">
                    <div class="tab-content active" id="pregnancy-schedule">
                        <div class="schedule-timeline">
                            <div class="timeline-item">
                                <div class="timeline-marker">
                                    <i class="fas fa-stethoscope"></i>
                                </div>
                                <div class="timeline-content">
                                    <h4>First Prenatal Visit</h4>
                                    <p class="timeline-time">6-8 weeks</p>
                                    <ul>
                                        <li>Confirm pregnancy</li>
                                        <li>Medical history review</li>
                                        <li>Physical examination</li>
                                        <li>Initial blood tests</li>
                                    </ul>
                                </div>
                            </div>
                            
                            <div class="timeline-item">
                                <div class="timeline-marker">
                                    <i class="fas fa-heartbeat"></i>
                                </div>
                                <div class="timeline-content">
                                    <h4>First Ultrasound</h4>
                                    <p class="timeline-time">8-12 weeks</p>
                                    <ul>
                                        <li>Dating scan</li>
                                        <li>Check baby's heartbeat</li>
                                        <li>Estimate due date</li>
                                        <li>Screen for abnormalities</li>
                                    </ul>
                                </div>
                            </div>
                            
                            <div class="timeline-item">
                                <div class="timeline-marker">
                                    <i class="fas fa-calendar-check"></i>
                                </div>
                                <div class="timeline-content">
                                    <h4>Regular Checkups</h4>
                                    <p class="timeline-time">12-28 weeks</p>
                                    <ul>
                                        <li>Monthly visits</li>
                                        <li>Monitor baby's growth</li>
                                        <li>Check blood pressure</li>
                                        <li>Urine tests</li>
                                    </ul>
                                </div>
                            </div>
                            
                            <div class="timeline-item">
                                <div class="timeline-marker">
                                    <i class="fas fa-search"></i>
                                </div>
                                <div class="timeline-content">
                                    <h4>Anatomy Scan</h4>
                                    <p class="timeline-time">18-22 weeks</p>
                                    <ul>
                                        <li>Detailed ultrasound</li>
                                        <li>Check organ development</li>
                                        <li>Determine baby's sex</li>
                                        <li>Assess placenta position</li>
                                    </ul>
                                </div>
                            </div>
                            
                            <div class="timeline-item">
                                <div class="timeline-marker">
                                    <i class="fas fa-vial"></i>
                                </div>
                                <div class="timeline-content">
                                    <h4>Glucose Screening</h4>
                                    <p class="timeline-time">24-28 weeks</p>
                                    <ul>
                                        <li>Test for gestational diabetes</li>
                                        <li>Blood sugar monitoring</li>
                                        <li>Dietary counseling if needed</li>
                                    </ul>
                                </div>
                            </div>
                            
                            <div class="timeline-item">
                                <div class="timeline-marker">
                                    <i class="fas fa-calendar-alt"></i>
                                </div>
                                <div class="timeline-content">
                                    <h4>Frequent Visits</h4>
                                    <p class="timeline-time">28-36 weeks</p>
                                    <ul>
                                        <li>Bi-weekly visits</li>
                                        <li>Monitor baby's position</li>
                                        <li>Check for preeclampsia</li>
                                        <li>Group B strep test</li>
                                    </ul>
                                </div>
                            </div>
                            
                            <div class="timeline-item">
                                <div class="timeline-marker">
                                    <i class="fas fa-baby"></i>
                                </div>
                                <div class="timeline-content">
                                    <h4>Final Preparations</h4>
                                    <p class="timeline-time">36-40 weeks</p>
                                    <ul>
                                        <li>Weekly visits</li>
                                        <li>Cervical checks</li>
                                        <li>Birth plan discussion</li>
                                        <li>Labor preparation</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="tab-content" id="baby-schedule">
                        <div class="schedule-timeline">
                            <div class="timeline-item">
                                <div class="timeline-marker">
                                    <i class="fas fa-baby"></i>
                                </div>
                                <div class="timeline-content">
                                    <h4>First Pediatric Visit</h4>
                                    <p class="timeline-time">3-5 days after birth</p>
                                    <ul>
                                        <li>Weight and growth check</li>
                                        <li>Feeding assessment</li>
                                        <li>Jaundice screening</li>
                                        <li>Newborn care guidance</li>
                                    </ul>
                                </div>
                            </div>
                            
                            <div class="timeline-item">
                                <div class="timeline-marker">
                                    <i class="fas fa-syringe"></i>
                                </div>
                                <div class="timeline-content">
                                    <h4>2-Week Checkup</h4>
                                    <p class="timeline-time">2 weeks</p>
                                    <ul>
                                        <li>Weight gain assessment</li>
                                        <li>Feeding evaluation</li>
                                        <li>Development check</li>
                                        <li>Parent questions</li>
                                    </ul>
                                </div>
                            </div>
                            
                            <div class="timeline-item">
                                <div class="timeline-marker">
                                    <i class="fas fa-chart-line"></i>
                                </div>
                                <div class="timeline-content">
                                    <h4>Regular Checkups</h4>
                                    <p class="timeline-time">2, 4, 6, 9, 12 months</p>
                                    <ul>
                                        <li>Growth measurements</li>
                                        <li>Developmental milestones</li>
                                        <li>Vaccinations</li>
                                        <li>Safety guidance</li>
                                    </ul>
                                </div>
                            </div>
                            
                            <div class="timeline-item">
                                <div class="timeline-marker">
                                    <i class="fas fa-tooth"></i>
                                </div>
                                <div class="timeline-content">
                                    <h4>First Dental Visit</h4>
                                    <p class="timeline-time">12 months or first tooth</p>
                                    <ul>
                                        <li>Oral health assessment</li>
                                        <li>Dental hygiene guidance</li>
                                        <li>Fluoride discussion</li>
                                        <li>Teething advice</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Book Appointment Modal -->
    <div class="modal" id="book-appointment-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Book New Appointment</h3>
                <button class="modal-close" onclick="closeBookingModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="book-appointment-form">
                    <div class="form-group">
                        <label for="appointment-type">Appointment Type</label>
                        <select id="appointment-type" name="type" required>
                            <option value="">Select appointment type</option>
                            <option value="prenatal">Prenatal Checkup</option>
                            <option value="ultrasound">Ultrasound</option>
                            <option value="specialist">Specialist Consultation</option>
                            <option value="pediatric">Pediatric Visit</option>
                            <option value="vaccination">Vaccination</option>
                            <option value="dental">Dental Checkup</option>
                            <option value="other">Other</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="doctor-select">Healthcare Provider</label>
                        <select id="doctor-select" name="doctor_id" required>
                            <option value="">Select provider</option>
                            <!-- Options will be populated dynamically -->
                        </select>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="appointment-date">Date</label>
                            <input type="date" id="appointment-date" name="date" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="appointment-time">Time</label>
                            <select id="appointment-time" name="time" required>
                                <option value="">Select time</option>
                                <!-- Time slots will be populated based on availability -->
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="appointment-reason">Reason for Visit</label>
                        <textarea id="appointment-reason" name="reason" rows="3" placeholder="Brief description of the reason for this appointment"></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="appointment-notes">Additional Notes</label>
                        <textarea id="appointment-notes" name="notes" rows="2" placeholder="Any additional information or special requests"></textarea>
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" class="btn btn-outline" onclick="closeBookingModal()">Cancel</button>
                        <button type="submit" class="btn btn-primary">Book Appointment</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit Appointment Modal -->
    <div class="modal" id="edit-appointment-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Edit Appointment</h3>
                <button class="modal-close" onclick="closeEditModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="edit-appointment-form">
                    <input type="hidden" id="edit-appointment-id">
                    <!-- Similar form fields as booking modal -->
                    <div class="form-actions">
                        <button type="button" class="btn btn-outline" onclick="closeEditModal()">Cancel</button>
                        <button type="submit" class="btn btn-primary">Update Appointment</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="footer-brand">
                        <i class="fas fa-baby"></i>
                        <span>Preg and Baby Care</span>
                    </div>
                    <p class="footer-description">
                        Your trusted companion for pregnancy and baby care with AI-powered guidance and expert advice.
                    </p>
                </div>
                
                <div class="footer-section">
                    <h4 class="footer-title">Quick Links</h4>
                    <ul class="footer-links">
                        <li><a href="pregnancy-care.html">Pregnancy Guide</a></li>
                        <li><a href="baby-care.html">Baby Care</a></li>
                        <li><a href="nutrition.html">Nutrition</a></li>
                        <li><a href="appointments.html">Appointments</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4 class="footer-title">Support</h4>
                    <ul class="footer-links">
                        <li><a href="contact.html">Contact Us</a></li>
                        <li><a href="privacy-policy.html">Privacy Policy</a></li>
                        <li><a href="terms-of-service.html">Terms of Service</a></li>
                        <li><a href="cookie-policy.html">Cookie Policy</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4 class="footer-title">Connect</h4>
                    <div class="social-links">
                        <a href="#" class="social-link"><i class="fab fa-facebook"></i></a>
                        <a href="#" class="social-link"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="social-link"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="social-link"><i class="fab fa-youtube"></i></a>
                    </div>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; 2024 Preg and Baby Care. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Chatbot -->
    <div class="chatbot-container" id="chatbot-container">
        <!-- Will be populated by chatbot.js -->
    </div>

    <!-- Back to Top Button -->
    <button class="back-to-top" id="back-to-top" aria-label="Back to top">
        <i class="fas fa-arrow-up"></i>
    </button>

    <!-- JavaScript Files -->
    <script src="../js/utils/config.js"></script>
    <script src="../js/utils/api.js"></script>
    <script src="../js/utils/auth.js"></script>
    <script src="../js/components/navbar.js"></script>
    <script src="../js/components/chatbot.js"></script>
    <script src="../js/pages/appointments.js"></script>
    <script src="../js/main.js"></script>
</body>
</html>
