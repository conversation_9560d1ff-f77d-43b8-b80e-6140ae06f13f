#!/usr/bin/env python3
"""
Admin routes for Preg and Baby Care application.
"""

from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from models.database import get_db_connection

admin_bp = Blueprint('admin', __name__)

def check_admin_role(user_id):
    """Check if user has admin role."""
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute('SELECT role FROM users WHERE id = ?', (user_id,))
    user = cursor.fetchone()
    conn.close()
    return user and user['role'] == 'admin'

@admin_bp.route('/nutrition-plans', methods=['POST'])
@jwt_required()
def create_nutrition_plan():
    """Create a new nutrition plan (admin only)."""
    try:
        user_id = get_jwt_identity()
        if not check_admin_role(user_id):
            return jsonify({'error': 'Admin access required'}), 403
        
        data = request.get_json()
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO nutrition_plans (title, description, trimester, baby_age_months,
                                       target_audience, calories_per_day, protein_grams,
                                       carbs_grams, fat_grams, fiber_grams, iron_mg,
                                       calcium_mg, folic_acid_mcg, created_by)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            data.get('title'), data.get('description'), data.get('trimester'),
            data.get('baby_age_months'), data.get('target_audience'),
            data.get('calories_per_day'), data.get('protein_grams'),
            data.get('carbs_grams'), data.get('fat_grams'), data.get('fiber_grams'),
            data.get('iron_mg'), data.get('calcium_mg'), data.get('folic_acid_mcg'),
            user_id
        ))
        
        plan_id = cursor.lastrowid
        conn.commit()
        conn.close()
        
        return jsonify({
            'message': 'Nutrition plan created successfully',
            'plan_id': plan_id
        }), 201
        
    except Exception as e:
        return jsonify({'error': f'Failed to create nutrition plan: {str(e)}'}), 500

@admin_bp.route('/meal-plans', methods=['POST'])
@jwt_required()
def create_meal_plan():
    """Create a new meal plan (admin only)."""
    try:
        user_id = get_jwt_identity()
        if not check_admin_role(user_id):
            return jsonify({'error': 'Admin access required'}), 403
        
        data = request.get_json()
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO daily_meal_plans (nutrition_plan_id, day_of_week, week_number,
                                        trimester, meal_type, main_dish, sides, drinks,
                                        nutrition_highlights, calories, preparation_time_minutes,
                                        difficulty_level, ingredients, instructions, tips)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            data.get('nutrition_plan_id'), data.get('day_of_week'), data.get('week_number'),
            data.get('trimester'), data.get('meal_type'), data.get('main_dish'),
            data.get('sides'), data.get('drinks'), data.get('nutrition_highlights'),
            data.get('calories'), data.get('preparation_time_minutes'),
            data.get('difficulty_level'), data.get('ingredients'),
            data.get('instructions'), data.get('tips')
        ))
        
        meal_plan_id = cursor.lastrowid
        conn.commit()
        conn.close()
        
        return jsonify({
            'message': 'Meal plan created successfully',
            'meal_plan_id': meal_plan_id
        }), 201
        
    except Exception as e:
        return jsonify({'error': f'Failed to create meal plan: {str(e)}'}), 500

@admin_bp.route('/users', methods=['GET'])
@jwt_required()
def get_users():
    """Get all users (admin only)."""
    try:
        user_id = get_jwt_identity()
        if not check_admin_role(user_id):
            return jsonify({'error': 'Admin access required'}), 403
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, email, first_name, last_name, role, is_pregnant,
                   created_at, last_login, is_active
            FROM users
            ORDER BY created_at DESC
        ''')
        
        users = cursor.fetchall()
        conn.close()
        
        return jsonify({
            'users': [dict(user) for user in users]
        }), 200
        
    except Exception as e:
        return jsonify({'error': f'Failed to get users: {str(e)}'}), 500
