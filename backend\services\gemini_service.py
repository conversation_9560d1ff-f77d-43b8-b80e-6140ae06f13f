#!/usr/bin/env python3
"""
Google Gemini AI service for Preg and Baby Care application.
Provides intelligent responses for pregnancy and baby care questions.
"""

import os
import google.generativeai as genai
from typing import Dict, List, Optional, Any
import json
from datetime import datetime, timedelta

class GeminiService:
    def __init__(self):
        """Initialize Gemini AI service."""
        self.api_key = os.getenv('GEMINI_API_KEY')
        if not self.api_key:
            raise ValueError("GEMINI_API_KEY environment variable is required")
        
        genai.configure(api_key=self.api_key)
        self.model = genai.GenerativeModel('gemini-pro')
        
        # System prompt for pregnancy and baby care context
        self.system_prompt = """
You are a helpful AI assistant specializing in pregnancy and baby care. You provide accurate, 
evidence-based information while being empathetic and supportive. 

IMPORTANT GUIDELINES:
1. Always recommend consulting healthcare providers for medical concerns
2. Provide general information, not personalized medical advice
3. Be encouraging and supportive in your responses
4. If asked about emergency situations, advise immediate medical attention
5. Focus on pregnancy, baby care, nutrition, development, and wellness topics
6. Use simple, clear language that's easy to understand

EMERGENCY KEYWORDS: If the user mentions severe pain, bleeding, difficulty breathing, 
high fever, or other emergency symptoms, immediately advise seeking emergency medical care.

Remember: You're here to inform and support, not to replace professional medical care.
"""

    def generate_response(self, message: str, user_context: Optional[Dict] = None, 
                         chat_history: Optional[List[Dict]] = None) -> Dict[str, Any]:
        """
        Generate a response using Gemini AI.
        
        Args:
            message: User's message
            user_context: User profile information
            chat_history: Previous conversation history
            
        Returns:
            Dict containing response and metadata
        """
        try:
            # Build context-aware prompt
            prompt = self._build_prompt(message, user_context, chat_history)
            
            # Check for emergency keywords
            emergency_keywords = [
                'severe pain', 'heavy bleeding', 'can\'t breathe', 'difficulty breathing',
                'high fever', 'emergency', 'urgent', 'help me', 'something wrong'
            ]
            
            is_emergency = any(keyword in message.lower() for keyword in emergency_keywords)
            
            if is_emergency:
                return {
                    'content': self._get_emergency_response(),
                    'type': 'emergency',
                    'timestamp': datetime.now().isoformat()
                }
            
            # Generate response
            response = self.model.generate_content(prompt)
            
            return {
                'content': response.text,
                'type': 'normal',
                'timestamp': datetime.now().isoformat(),
                'user_context_used': user_context is not None
            }
            
        except Exception as e:
            return {
                'content': self._get_fallback_response(),
                'type': 'error',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }

    def _build_prompt(self, message: str, user_context: Optional[Dict] = None, 
                     chat_history: Optional[List[Dict]] = None) -> str:
        """Build a context-aware prompt for Gemini."""
        
        prompt_parts = [self.system_prompt]
        
        # Add user context if available
        if user_context:
            context_info = []
            
            if user_context.get('first_name'):
                context_info.append(f"User's name: {user_context['first_name']}")
            
            if user_context.get('is_pregnant'):
                context_info.append("User is currently pregnant")
                
                if user_context.get('due_date'):
                    due_date = datetime.strptime(user_context['due_date'], '%Y-%m-%d')
                    today = datetime.now()
                    weeks_pregnant = (280 - (due_date - today).days) // 7
                    
                    if weeks_pregnant > 0:
                        context_info.append(f"Approximately {weeks_pregnant} weeks pregnant")
                        
                        if weeks_pregnant <= 12:
                            context_info.append("Currently in first trimester")
                        elif weeks_pregnant <= 28:
                            context_info.append("Currently in second trimester")
                        else:
                            context_info.append("Currently in third trimester")
            
            elif user_context.get('baby_birth_date'):
                birth_date = datetime.strptime(user_context['baby_birth_date'], '%Y-%m-%d')
                baby_age_days = (datetime.now() - birth_date).days
                baby_age_months = baby_age_days // 30
                
                if baby_age_months < 1:
                    context_info.append(f"Has a newborn baby ({baby_age_days} days old)")
                else:
                    context_info.append(f"Has a {baby_age_months}-month-old baby")
            
            if context_info:
                prompt_parts.append(f"\nUSER CONTEXT:\n{chr(10).join(context_info)}")
        
        # Add chat history for context
        if chat_history and len(chat_history) > 0:
            history_text = "\nRECENT CONVERSATION:\n"
            for msg in chat_history[-5:]:  # Last 5 messages
                role = "User" if msg['role'] == 'user' else "Assistant"
                history_text += f"{role}: {msg['content']}\n"
            prompt_parts.append(history_text)
        
        # Add current message
        prompt_parts.append(f"\nCURRENT USER MESSAGE:\n{message}")
        
        # Add response guidelines
        prompt_parts.append("""
\nPLEASE RESPOND WITH:
- Helpful, accurate information
- Empathy and understanding
- Clear recommendations to consult healthcare providers when appropriate
- Encouragement and support
- Practical tips when relevant
""")
        
        return "\n".join(prompt_parts)

    def _get_emergency_response(self) -> str:
        """Get emergency response template."""
        return """
🚨 **IMPORTANT: This sounds like it could be an emergency situation.**

**Please seek immediate medical attention by:**
- Calling your doctor or midwife right away
- Going to the nearest emergency room
- Calling emergency services (911) if needed

**Don't wait or try to self-diagnose emergency symptoms.**

I'm here to provide general information and support, but emergency situations require immediate professional medical care. Your health and safety are the top priority.

After you've received proper medical care, I'm here to help with any questions about recovery or ongoing care.
"""

    def _get_fallback_response(self) -> str:
        """Get fallback response when AI service fails."""
        return """
I apologize, but I'm having trouble processing your request right now. Here are some helpful resources:

**For immediate concerns:**
- Contact your healthcare provider
- Call your doctor's office or midwife
- Visit urgent care or emergency room if needed

**For general information:**
- Check reputable pregnancy/baby care websites
- Consult pregnancy/parenting books
- Join support groups or forums

I'll be back to help you soon. Thank you for your patience!
"""

    def analyze_sentiment(self, message: str) -> Dict[str, Any]:
        """Analyze the sentiment of a user message."""
        try:
            prompt = f"""
Analyze the sentiment and emotional tone of this message from a pregnancy/baby care context:

Message: "{message}"

Provide a brief analysis including:
1. Overall sentiment (positive, negative, neutral, concerned, anxious)
2. Emotional indicators
3. Urgency level (low, medium, high)
4. Suggested response tone

Keep the analysis concise and focused on helping provide appropriate support.
"""
            
            response = self.model.generate_content(prompt)
            
            return {
                'analysis': response.text,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                'analysis': 'Unable to analyze sentiment',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }

    def get_topic_suggestions(self, user_context: Optional[Dict] = None) -> List[str]:
        """Get topic suggestions based on user context."""
        
        general_topics = [
            "Nutrition during pregnancy",
            "Safe exercises for expecting mothers",
            "Preparing for labor and delivery",
            "Newborn care basics",
            "Breastfeeding tips",
            "Baby sleep patterns",
            "Postpartum recovery"
        ]
        
        if not user_context:
            return general_topics
        
        suggestions = []
        
        if user_context.get('is_pregnant'):
            # Calculate trimester and suggest relevant topics
            if user_context.get('due_date'):
                due_date = datetime.strptime(user_context['due_date'], '%Y-%m-%d')
                weeks_pregnant = (280 - (due_date - datetime.now()).days) // 7
                
                if weeks_pregnant <= 12:
                    suggestions.extend([
                        "First trimester nutrition",
                        "Managing morning sickness",
                        "Early pregnancy symptoms",
                        "Prenatal vitamins"
                    ])
                elif weeks_pregnant <= 28:
                    suggestions.extend([
                        "Second trimester development",
                        "Anatomy scan preparation",
                        "Pregnancy exercises",
                        "Maternity clothes"
                    ])
                else:
                    suggestions.extend([
                        "Third trimester preparation",
                        "Labor signs to watch for",
                        "Hospital bag checklist",
                        "Birth plan considerations"
                    ])
        
        elif user_context.get('baby_birth_date'):
            birth_date = datetime.strptime(user_context['baby_birth_date'], '%Y-%m-%d')
            baby_age_months = (datetime.now() - birth_date).days // 30
            
            if baby_age_months < 3:
                suggestions.extend([
                    "Newborn feeding schedules",
                    "Sleep training basics",
                    "Postpartum recovery",
                    "Baby development milestones"
                ])
            elif baby_age_months < 12:
                suggestions.extend([
                    "Introducing solid foods",
                    "Baby-proofing your home",
                    "Vaccination schedules",
                    "Developmental activities"
                ])
        
        return suggestions[:8]  # Return top 8 suggestions
