/* Components CSS for Preg and Baby Care Application */

/* Navigation Bar */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: var(--z-fixed);
    background-color: var(--white);
    border-bottom: 1px solid var(--gray-200);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-fast);
}

.navbar.scrolled {
    box-shadow: var(--shadow-md);
}

.nav-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 var(--container-padding);
    height: 4rem;
}

.nav-brand {
    display: flex;
    align-items: center;
}

.brand-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-bold);
    color: var(--primary-color);
    text-decoration: none;
}

.brand-icon {
    font-size: var(--font-size-xl);
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: var(--spacing-xl);
}

.nav-section {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.nav-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--gray-700);
    text-decoration: none;
    border-radius: var(--border-radius-md);
    transition: all var(--transition-fast);
}

.nav-link:hover {
    color: var(--primary-color);
    background-color: var(--primary-light);
}

.nav-dropdown {
    position: relative;
}

.dropdown-toggle {
    cursor: pointer;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    min-width: 200px;
    background-color: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-lg);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all var(--transition-fast);
    z-index: var(--z-dropdown);
}

.nav-dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
    font-size: var(--font-size-sm);
    color: var(--gray-700);
    text-decoration: none;
    transition: all var(--transition-fast);
}

.dropdown-item:hover {
    color: var(--primary-color);
    background-color: var(--gray-50);
}

.nav-auth {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    padding: var(--spacing-sm);
}

.bar {
    width: 1.5rem;
    height: 2px;
    background-color: var(--gray-700);
    margin: 2px 0;
    transition: all var(--transition-fast);
}

/* Hero Section */
.hero {
    padding: 8rem 0 4rem;
    background: linear-gradient(135deg, var(--primary-light) 0%, var(--secondary-light) 100%);
    overflow: hidden;
}

.hero-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-3xl);
    align-items: center;
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 var(--container-padding);
}

.hero-content {
    max-width: 500px;
}

.hero-title {
    font-size: var(--font-size-5xl);
    font-weight: var(--font-weight-bold);
    line-height: var(--line-height-tight);
    color: var(--gray-900);
    margin-bottom: var(--spacing-lg);
}

.highlight {
    color: var(--primary-color);
}

.hero-description {
    font-size: var(--font-size-lg);
    color: var(--gray-600);
    margin-bottom: var(--spacing-xl);
    line-height: var(--line-height-relaxed);
}

.hero-actions {
    display: flex;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

.hero-image {
    display: flex;
    justify-content: center;
    align-items: center;
}

.hero-illustration {
    position: relative;
    width: 300px;
    height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--white) 0%, var(--gray-50) 100%);
    border-radius: 50%;
    box-shadow: var(--shadow-xl);
}

.hero-icon {
    font-size: 6rem;
    color: var(--primary-color);
}

.floating-elements {
    position: absolute;
    inset: 0;
}

.floating-element {
    position: absolute;
    width: 3rem;
    height: 3rem;
    background-color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-md);
    animation: float 3s ease-in-out infinite;
}

.floating-element:nth-child(1) {
    top: 20%;
    right: 10%;
    animation-delay: 0s;
}

.floating-element:nth-child(2) {
    bottom: 30%;
    left: 5%;
    animation-delay: 1s;
}

.floating-element:nth-child(3) {
    top: 60%;
    right: 20%;
    animation-delay: 2s;
}

.floating-element i {
    color: var(--primary-color);
    font-size: var(--font-size-lg);
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

/* Features Section */
.features {
    padding: var(--spacing-3xl) 0;
    background-color: var(--white);
}

.section-header {
    text-align: center;
    margin-bottom: var(--spacing-3xl);
}

.section-title {
    font-size: var(--font-size-4xl);
    font-weight: var(--font-weight-bold);
    color: var(--gray-900);
    margin-bottom: var(--spacing-md);
}

.section-description {
    font-size: var(--font-size-lg);
    color: var(--gray-600);
    max-width: 600px;
    margin: 0 auto;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 var(--container-padding);
}

.feature-card {
    background-color: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    text-align: center;
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-sm);
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.feature-icon {
    width: 4rem;
    height: 4rem;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-lg);
}

.feature-icon i {
    font-size: var(--font-size-xl);
    color: var(--white);
}

.feature-title {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    color: var(--gray-900);
    margin-bottom: var(--spacing-md);
}

.feature-description {
    color: var(--gray-600);
    line-height: var(--line-height-relaxed);
    margin-bottom: var(--spacing-lg);
}

.feature-link {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    color: var(--primary-color);
    font-weight: var(--font-weight-medium);
    text-decoration: none;
    transition: all var(--transition-fast);
}

.feature-link:hover {
    color: var(--primary-dark);
    gap: var(--spacing-sm);
}

/* Stats Section */
.stats {
    padding: var(--spacing-3xl) 0;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: var(--white);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-xl);
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 var(--container-padding);
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: var(--font-size-4xl);
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--spacing-sm);
}

.stat-label {
    font-size: var(--font-size-lg);
    opacity: 0.9;
}

/* CTA Section */
.cta {
    padding: var(--spacing-3xl) 0;
    background-color: var(--gray-50);
}

.cta-content {
    text-align: center;
    max-width: 600px;
    margin: 0 auto;
    padding: 0 var(--container-padding);
}

.cta-title {
    font-size: var(--font-size-4xl);
    font-weight: var(--font-weight-bold);
    color: var(--gray-900);
    margin-bottom: var(--spacing-md);
}

.cta-description {
    font-size: var(--font-size-lg);
    color: var(--gray-600);
    margin-bottom: var(--spacing-xl);
    line-height: var(--line-height-relaxed);
}

.cta-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: center;
    flex-wrap: wrap;
}

/* Footer */
.footer {
    background-color: var(--gray-900);
    color: var(--white);
    padding: var(--spacing-3xl) 0 var(--spacing-lg);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-xl);
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 var(--container-padding);
}

.footer-section {
    display: flex;
    flex-direction: column;
}

.footer-brand {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-bold);
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
}

.footer-description {
    color: var(--gray-400);
    line-height: var(--line-height-relaxed);
}

.footer-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    margin-bottom: var(--spacing-md);
    color: var(--white);
}

.footer-links {
    list-style: none;
    padding: 0;
}

.footer-links li {
    margin-bottom: var(--spacing-sm);
}

.footer-links a {
    color: var(--gray-400);
    text-decoration: none;
    transition: color var(--transition-fast);
}

.footer-links a:hover {
    color: var(--primary-color);
}

.social-links {
    display: flex;
    gap: var(--spacing-md);
}

.social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    background-color: var(--gray-800);
    color: var(--gray-400);
    border-radius: 50%;
    text-decoration: none;
    transition: all var(--transition-fast);
}

.social-link:hover {
    background-color: var(--primary-color);
    color: var(--white);
}

.footer-bottom {
    margin-top: var(--spacing-xl);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--gray-800);
    text-align: center;
    color: var(--gray-400);
}

/* Auth Pages */
.auth-page {
    background: linear-gradient(135deg, var(--primary-light) 0%, var(--secondary-light) 100%);
    min-height: 100vh;
}

.navbar-minimal {
    position: relative;
    background-color: transparent;
    border-bottom: none;
    box-shadow: none;
}

.auth-main {
    padding: 6rem 0 2rem;
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.auth-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-3xl);
    max-width: 1000px;
    margin: 0 auto;
    padding: 0 var(--container-padding);
}

.auth-card {
    background-color: var(--white);
    border-radius: var(--border-radius-xl);
    padding: var(--spacing-3xl);
    box-shadow: var(--shadow-xl);
}

.auth-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.auth-title {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    color: var(--gray-900);
    margin-bottom: var(--spacing-sm);
}

.auth-subtitle {
    color: var(--gray-600);
    font-size: var(--font-size-base);
}

.auth-form {
    margin-bottom: var(--spacing-xl);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-md);
}

.password-input-container {
    position: relative;
}

.password-toggle {
    position: absolute;
    right: var(--spacing-md);
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--gray-500);
    cursor: pointer;
    padding: var(--spacing-xs);
}

.password-strength {
    margin-top: var(--spacing-xs);
    font-size: var(--font-size-sm);
}

.password-strength.weak { color: var(--error-color); }
.password-strength.medium { color: var(--warning-color); }
.password-strength.strong { color: var(--success-color); }

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
}

.checkbox-label, .radio-label {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-sm);
    color: var(--gray-700);
    cursor: pointer;
}

.checkbox-custom, .radio-custom {
    width: 1rem;
    height: 1rem;
    border: 2px solid var(--gray-300);
    border-radius: var(--border-radius-sm);
    position: relative;
    flex-shrink: 0;
}

.radio-custom {
    border-radius: 50%;
}

.checkbox-label input[type="checkbox"],
.radio-label input[type="radio"] {
    position: absolute;
    opacity: 0;
    cursor: pointer;
}

.checkbox-label input[type="checkbox"]:checked + .checkbox-custom::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--primary-color);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-bold);
}

.radio-label input[type="radio"]:checked + .radio-custom::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 0.5rem;
    height: 0.5rem;
    background-color: var(--primary-color);
    border-radius: 50%;
}

.radio-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.forgot-password-link {
    color: var(--primary-color);
    font-size: var(--font-size-sm);
    text-decoration: none;
}

.forgot-password-link:hover {
    text-decoration: underline;
}

.auth-divider {
    position: relative;
    text-align: center;
    margin: var(--spacing-xl) 0;
}

.auth-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background-color: var(--gray-300);
}

.auth-divider span {
    background-color: var(--white);
    color: var(--gray-500);
    padding: 0 var(--spacing-md);
    font-size: var(--font-size-sm);
}

.social-login {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
}

.btn-social {
    justify-content: flex-start;
    padding: var(--spacing-md) var(--spacing-lg);
    border: 1px solid var(--gray-300);
    background-color: var(--white);
    color: var(--gray-700);
}

.btn-google:hover {
    background-color: var(--gray-50);
    border-color: var(--gray-400);
}

.btn-facebook {
    background-color: #1877f2;
    border-color: #1877f2;
    color: var(--white);
}

.btn-facebook:hover {
    background-color: #166fe5;
    border-color: #166fe5;
}

.auth-footer {
    text-align: center;
}

.auth-link {
    color: var(--primary-color);
    font-weight: var(--font-weight-medium);
    text-decoration: none;
}

.auth-link:hover {
    text-decoration: underline;
}

.auth-benefits {
    padding: var(--spacing-xl);
}

.auth-benefits h3 {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    color: var(--gray-900);
    margin-bottom: var(--spacing-lg);
}

.benefits-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.benefit-item {
    display: flex;
    gap: var(--spacing-md);
    align-items: flex-start;
}

.benefit-item i {
    color: var(--primary-color);
    font-size: var(--font-size-lg);
    margin-top: var(--spacing-xs);
    flex-shrink: 0;
}

.benefit-item h4 {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    color: var(--gray-900);
    margin-bottom: var(--spacing-xs);
}

.benefit-item p {
    color: var(--gray-600);
    font-size: var(--font-size-sm);
    line-height: var(--line-height-relaxed);
    margin: 0;
}

.footer-minimal {
    background-color: transparent;
    padding: var(--spacing-lg) 0;
}

.footer-minimal .footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.footer-minimal .footer-links {
    display: flex;
    gap: var(--spacing-lg);
    list-style: none;
    margin: 0;
    padding: 0;
}

.footer-minimal .footer-links a {
    color: var(--gray-600);
    font-size: var(--font-size-sm);
}

.footer-minimal p {
    color: var(--gray-600);
    font-size: var(--font-size-sm);
    margin: 0;
}
