#!/usr/bin/env python3
"""
Nutrition routes for Preg and Baby Care application.
Handles nutrition plans, recommendations, and dietary information.
"""

from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from models.database import get_db_connection

nutrition_bp = Blueprint('nutrition', __name__)

@nutrition_bp.route('/plans', methods=['GET'])
def get_nutrition_plans():
    """Get all active nutrition plans."""
    try:
        trimester = request.args.get('trimester', type=int)
        baby_age_months = request.args.get('baby_age_months', type=int)
        target_audience = request.args.get('target_audience')
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        query = 'SELECT * FROM nutrition_plans WHERE is_active = 1'
        params = []
        
        if trimester:
            query += ' AND (trimester = ? OR trimester IS NULL)'
            params.append(trimester)
        
        if baby_age_months is not None:
            query += ' AND (baby_age_months = ? OR baby_age_months IS NULL)'
            params.append(baby_age_months)
        
        if target_audience:
            query += ' AND target_audience = ?'
            params.append(target_audience)
        
        query += ' ORDER BY created_at DESC'
        
        cursor.execute(query, params)
        plans = cursor.fetchall()
        conn.close()
        
        return jsonify({
            'plans': [dict(plan) for plan in plans]
        }), 200
        
    except Exception as e:
        return jsonify({'error': f'Failed to get nutrition plans: {str(e)}'}), 500

@nutrition_bp.route('/plans/<int:plan_id>', methods=['GET'])
def get_nutrition_plan(plan_id):
    """Get a specific nutrition plan by ID."""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM nutrition_plans WHERE id = ? AND is_active = 1', (plan_id,))
        plan = cursor.fetchone()
        
        if not plan:
            conn.close()
            return jsonify({'error': 'Nutrition plan not found'}), 404
        
        conn.close()
        return jsonify({'plan': dict(plan)}), 200
        
    except Exception as e:
        return jsonify({'error': f'Failed to get nutrition plan: {str(e)}'}), 500

@nutrition_bp.route('/recommendations', methods=['GET'])
@jwt_required()
def get_nutrition_recommendations():
    """Get personalized nutrition recommendations for the user."""
    try:
        user_id = get_jwt_identity()
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Get user information
        cursor.execute('''
            SELECT u.is_pregnant, u.due_date, u.baby_birth_date,
                   p.height_cm, p.pre_pregnancy_weight_kg
            FROM users u
            LEFT JOIN user_profiles p ON u.id = p.user_id
            WHERE u.id = ?
        ''', (user_id,))
        
        user = cursor.fetchone()
        if not user:
            conn.close()
            return jsonify({'error': 'User not found'}), 404
        
        recommendations = []
        
        if user['is_pregnant'] and user['due_date']:
            # Calculate trimester based on due date
            from datetime import datetime, timedelta
            due_date = datetime.strptime(user['due_date'], '%Y-%m-%d')
            today = datetime.now()
            weeks_pregnant = (280 - (due_date - today).days) // 7
            
            if weeks_pregnant <= 12:
                trimester = 1
            elif weeks_pregnant <= 28:
                trimester = 2
            else:
                trimester = 3
            
            # Get pregnancy nutrition plans
            cursor.execute('''
                SELECT * FROM nutrition_plans 
                WHERE target_audience IN ('pregnancy', 'both') 
                AND (trimester = ? OR trimester IS NULL)
                AND is_active = 1
                ORDER BY trimester, created_at DESC
            ''', (trimester,))
            
            plans = cursor.fetchall()
            
            recommendations.append({
                'category': 'Pregnancy Nutrition',
                'trimester': trimester,
                'weeks_pregnant': weeks_pregnant,
                'plans': [dict(plan) for plan in plans]
            })
            
        elif user['baby_birth_date']:
            # Calculate baby age
            birth_date = datetime.strptime(user['baby_birth_date'], '%Y-%m-%d')
            baby_age_months = (datetime.now() - birth_date).days // 30
            
            # Get baby nutrition plans
            cursor.execute('''
                SELECT * FROM nutrition_plans 
                WHERE target_audience IN ('baby', 'both')
                AND (baby_age_months = ? OR baby_age_months IS NULL)
                AND is_active = 1
                ORDER BY baby_age_months, created_at DESC
            ''', (baby_age_months,))
            
            plans = cursor.fetchall()
            
            recommendations.append({
                'category': 'Baby Nutrition',
                'baby_age_months': baby_age_months,
                'plans': [dict(plan) for plan in plans]
            })
        
        # Get general nutrition plans
        cursor.execute('''
            SELECT * FROM nutrition_plans 
            WHERE target_audience = 'both'
            AND trimester IS NULL
            AND baby_age_months IS NULL
            AND is_active = 1
            ORDER BY created_at DESC
            LIMIT 5
        ''')
        
        general_plans = cursor.fetchall()
        if general_plans:
            recommendations.append({
                'category': 'General Nutrition',
                'plans': [dict(plan) for plan in general_plans]
            })
        
        conn.close()
        
        return jsonify({
            'recommendations': recommendations,
            'user_info': {
                'is_pregnant': bool(user['is_pregnant']),
                'height_cm': user['height_cm'],
                'pre_pregnancy_weight_kg': user['pre_pregnancy_weight_kg']
            }
        }), 200
        
    except Exception as e:
        return jsonify({'error': f'Failed to get recommendations: {str(e)}'}), 500

@nutrition_bp.route('/daily-requirements', methods=['GET'])
@jwt_required()
def get_daily_requirements():
    """Get daily nutritional requirements based on user profile."""
    try:
        user_id = get_jwt_identity()
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT u.is_pregnant, u.due_date, u.baby_birth_date,
                   p.height_cm, p.pre_pregnancy_weight_kg
            FROM users u
            LEFT JOIN user_profiles p ON u.id = p.user_id
            WHERE u.id = ?
        ''', (user_id,))
        
        user = cursor.fetchone()
        if not user:
            conn.close()
            return jsonify({'error': 'User not found'}), 404
        
        # Base requirements for adult women
        requirements = {
            'calories': 2000,
            'protein_grams': 50,
            'carbs_grams': 250,
            'fat_grams': 65,
            'fiber_grams': 25,
            'iron_mg': 18,
            'calcium_mg': 1000,
            'folic_acid_mcg': 400
        }
        
        if user['is_pregnant']:
            # Adjust for pregnancy
            requirements.update({
                'calories': 2200,  # Additional 200 calories
                'protein_grams': 70,  # Additional 20g
                'iron_mg': 27,  # Increased iron
                'folic_acid_mcg': 600,  # Increased folic acid
                'calcium_mg': 1200  # Increased calcium
            })
        
        conn.close()
        
        return jsonify({
            'daily_requirements': requirements,
            'notes': [
                'These are general recommendations',
                'Consult with your healthcare provider for personalized advice',
                'Requirements may vary based on individual health conditions'
            ]
        }), 200
        
    except Exception as e:
        return jsonify({'error': f'Failed to get daily requirements: {str(e)}'}), 500

@nutrition_bp.route('/food-safety', methods=['GET'])
def get_food_safety_info():
    """Get food safety information for pregnancy and baby care."""
    try:
        category = request.args.get('category', 'pregnancy')  # pregnancy or baby
        
        if category == 'pregnancy':
            info = {
                'foods_to_avoid': [
                    'Raw or undercooked meat, poultry, and seafood',
                    'Raw or lightly cooked eggs',
                    'Unpasteurized dairy products',
                    'High-mercury fish (shark, swordfish, king mackerel)',
                    'Raw sprouts',
                    'Unwashed fruits and vegetables',
                    'Alcohol',
                    'Excessive caffeine (limit to 200mg/day)'
                ],
                'safe_foods': [
                    'Well-cooked meat, poultry, and seafood',
                    'Pasteurized dairy products',
                    'Low-mercury fish (salmon, sardines, anchovies)',
                    'Thoroughly washed fruits and vegetables',
                    'Whole grains',
                    'Legumes and nuts',
                    'Prenatal vitamins as recommended'
                ],
                'preparation_tips': [
                    'Wash hands frequently',
                    'Use separate cutting boards for raw meat',
                    'Cook foods to safe internal temperatures',
                    'Refrigerate perishables promptly',
                    'Avoid cross-contamination'
                ]
            }
        else:  # baby
            info = {
                'introduction_timeline': {
                    '0-6_months': 'Breast milk or formula only',
                    '6_months': 'Start with single-ingredient purees',
                    '8-10_months': 'Finger foods and combination foods',
                    '12_months': 'Most family foods, whole milk'
                },
                'first_foods': [
                    'Iron-fortified cereal',
                    'Pureed vegetables (sweet potato, carrots)',
                    'Pureed fruits (banana, apple, pear)',
                    'Pureed meats'
                ],
                'foods_to_avoid_first_year': [
                    'Honey (risk of botulism)',
                    'Whole nuts and seeds (choking hazard)',
                    'Cow\'s milk as main drink (before 12 months)',
                    'Added salt and sugar',
                    'Hard candies and gum'
                ],
                'allergy_prevention': [
                    'Introduce common allergens early (around 6 months)',
                    'Include peanuts, eggs, fish, and dairy',
                    'Watch for allergic reactions',
                    'Consult pediatrician about family history'
                ]
            }
        
        return jsonify({
            'category': category,
            'food_safety_info': info
        }), 200
        
    except Exception as e:
        return jsonify({'error': f'Failed to get food safety info: {str(e)}'}), 500
