/**
 * API utility functions for Preg and Baby Care application
 */

class ApiClient {
    constructor() {
        this.baseURL = API_CONFIG.BASE_URL;
        this.timeout = API_CONFIG.TIMEOUT;
        this.retryAttempts = API_CONFIG.RETRY_ATTEMPTS;
        this.retryDelay = API_CONFIG.RETRY_DELAY;
    }

    /**
     * Get authentication token from localStorage
     */
    getAuthToken() {
        return localStorage.getItem(APP_CONFIG.STORAGE_KEYS.AUTH_TOKEN);
    }

    /**
     * Set authentication token in localStorage
     */
    setAuthToken(token) {
        if (token) {
            localStorage.setItem(APP_CONFIG.STORAGE_KEYS.AUTH_TOKEN, token);
        } else {
            localStorage.removeItem(APP_CONFIG.STORAGE_KEYS.AUTH_TOKEN);
        }
    }

    /**
     * Get default headers for API requests
     */
    getDefaultHeaders() {
        const headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
        };

        const token = this.getAuthToken();
        if (token) {
            headers['Authorization'] = `Bearer ${token}`;
        }

        return headers;
    }

    /**
     * Handle API response
     */
    async handleResponse(response) {
        const contentType = response.headers.get('content-type');
        let data;

        if (contentType && contentType.includes('application/json')) {
            data = await response.json();
        } else {
            data = await response.text();
        }

        if (!response.ok) {
            const error = new Error(data.message || data.error || ERROR_MESSAGES.GENERIC_ERROR);
            error.status = response.status;
            error.data = data;
            throw error;
        }

        return data;
    }

    /**
     * Make HTTP request with retry logic
     */
    async makeRequest(url, options = {}, attempt = 1) {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), this.timeout);

        try {
            const response = await fetch(url, {
                ...options,
                signal: controller.signal,
                headers: {
                    ...this.getDefaultHeaders(),
                    ...options.headers,
                },
            });

            clearTimeout(timeoutId);
            return await this.handleResponse(response);
        } catch (error) {
            clearTimeout(timeoutId);

            // Handle specific error types
            if (error.name === 'AbortError') {
                throw new Error('Request timeout');
            }

            // Retry logic for network errors
            if (attempt < this.retryAttempts && this.shouldRetry(error)) {
                await this.delay(this.retryDelay * attempt);
                return this.makeRequest(url, options, attempt + 1);
            }

            // Handle authentication errors
            if (error.status === 401) {
                this.handleAuthError();
                throw new Error(ERROR_MESSAGES.UNAUTHORIZED);
            }

            throw error;
        }
    }

    /**
     * Determine if request should be retried
     */
    shouldRetry(error) {
        // Retry on network errors or 5xx server errors
        return !error.status || (error.status >= 500 && error.status < 600);
    }

    /**
     * Handle authentication errors
     */
    handleAuthError() {
        this.setAuthToken(null);
        localStorage.removeItem(APP_CONFIG.STORAGE_KEYS.USER_DATA);
        
        // Redirect to login page if not already there
        if (!window.location.pathname.includes('login')) {
            window.location.href = '/pages/login.html';
        }
    }

    /**
     * Delay function for retry logic
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * GET request
     */
    async get(endpoint, params = {}) {
        const url = new URL(`${this.baseURL}${endpoint}`);
        Object.keys(params).forEach(key => {
            if (params[key] !== undefined && params[key] !== null) {
                url.searchParams.append(key, params[key]);
            }
        });

        return this.makeRequest(url.toString(), {
            method: 'GET',
        });
    }

    /**
     * POST request
     */
    async post(endpoint, data = {}) {
        return this.makeRequest(`${this.baseURL}${endpoint}`, {
            method: 'POST',
            body: JSON.stringify(data),
        });
    }

    /**
     * PUT request
     */
    async put(endpoint, data = {}) {
        return this.makeRequest(`${this.baseURL}${endpoint}`, {
            method: 'PUT',
            body: JSON.stringify(data),
        });
    }

    /**
     * PATCH request
     */
    async patch(endpoint, data = {}) {
        return this.makeRequest(`${this.baseURL}${endpoint}`, {
            method: 'PATCH',
            body: JSON.stringify(data),
        });
    }

    /**
     * DELETE request
     */
    async delete(endpoint) {
        return this.makeRequest(`${this.baseURL}${endpoint}`, {
            method: 'DELETE',
        });
    }

    /**
     * Upload file
     */
    async uploadFile(endpoint, file, additionalData = {}) {
        const formData = new FormData();
        formData.append('file', file);
        
        Object.keys(additionalData).forEach(key => {
            formData.append(key, additionalData[key]);
        });

        const headers = {};
        const token = this.getAuthToken();
        if (token) {
            headers['Authorization'] = `Bearer ${token}`;
        }

        return this.makeRequest(`${this.baseURL}${endpoint}`, {
            method: 'POST',
            body: formData,
            headers,
        });
    }
}

// Create global API client instance
const api = new ApiClient();

// API service functions
const ApiService = {
    // Authentication
    auth: {
        login: (credentials) => api.post(API_ENDPOINTS.AUTH.LOGIN, credentials),
        register: (userData) => api.post(API_ENDPOINTS.AUTH.REGISTER, userData),
        logout: () => api.post(API_ENDPOINTS.AUTH.LOGOUT),
        refreshToken: () => api.post(API_ENDPOINTS.AUTH.REFRESH),
        getProfile: () => api.get(API_ENDPOINTS.AUTH.PROFILE),
        updateProfile: (data) => api.put(API_ENDPOINTS.AUTH.PROFILE, data),
        changePassword: (data) => api.post(API_ENDPOINTS.AUTH.CHANGE_PASSWORD, data),
        forgotPassword: (email) => api.post(API_ENDPOINTS.AUTH.FORGOT_PASSWORD, { email }),
        resetPassword: (data) => api.post(API_ENDPOINTS.AUTH.RESET_PASSWORD, data),
    },

    // Nutrition
    nutrition: {
        getPlans: (params) => api.get(API_ENDPOINTS.NUTRITION.PLANS, params),
        getRecommendations: (params) => api.get(API_ENDPOINTS.NUTRITION.RECOMMENDATIONS, params),
        getFoodSafety: (params) => api.get(API_ENDPOINTS.NUTRITION.FOOD_SAFETY, params),
        getDailyRequirements: (params) => api.get(API_ENDPOINTS.NUTRITION.DAILY_REQUIREMENTS, params),
    },

    // Weight Tracking
    weight: {
        getRecords: (params) => api.get(API_ENDPOINTS.WEIGHT.RECORDS, params),
        addRecord: (data) => api.post(API_ENDPOINTS.WEIGHT.ADD_RECORD, data),
        updateRecord: (id, data) => api.put(`${API_ENDPOINTS.WEIGHT.UPDATE_RECORD}/${id}`, data),
        deleteRecord: (id) => api.delete(`${API_ENDPOINTS.WEIGHT.DELETE_RECORD}/${id}`),
        getExpectedGain: (params) => api.get(API_ENDPOINTS.WEIGHT.EXPECTED_GAIN, params),
        getStatistics: (params) => api.get(API_ENDPOINTS.WEIGHT.STATISTICS, params),
    },

    // Meal Plans
    mealPlans: {
        getDaily: (params) => api.get(API_ENDPOINTS.MEAL_PLANS.DAILY, params),
        getWeekly: (params) => api.get(API_ENDPOINTS.MEAL_PLANS.WEEKLY, params),
        getToday: () => api.get(API_ENDPOINTS.MEAL_PLANS.TODAY),
        getShoppingList: (params) => api.get(API_ENDPOINTS.MEAL_PLANS.SHOPPING_LIST, params),
        getFavorites: () => api.get(API_ENDPOINTS.MEAL_PLANS.FAVORITES),
        addToFavorites: (id) => api.post(`${API_ENDPOINTS.MEAL_PLANS.FAVORITES}/${id}`),
        removeFromFavorites: (id) => api.delete(`${API_ENDPOINTS.MEAL_PLANS.FAVORITES}/${id}`),
    },

    // Chatbot
    chatbot: {
        sendMessage: (data) => api.post(API_ENDPOINTS.CHATBOT.CHAT, data),
        getHistory: (params) => api.get(API_ENDPOINTS.CHATBOT.HISTORY, params),
        getSessions: () => api.get(API_ENDPOINTS.CHATBOT.SESSIONS),
        getQuickQuestions: (params) => api.get(API_ENDPOINTS.CHATBOT.QUICK_QUESTIONS, params),
        submitFeedback: (data) => api.post(API_ENDPOINTS.CHATBOT.FEEDBACK, data),
    },

    // Appointments
    appointments: {
        getList: (params) => api.get(API_ENDPOINTS.APPOINTMENTS.LIST, params),
        create: (data) => api.post(API_ENDPOINTS.APPOINTMENTS.CREATE, data),
        update: (id, data) => api.put(`${API_ENDPOINTS.APPOINTMENTS.UPDATE}/${id}`, data),
        delete: (id) => api.delete(`${API_ENDPOINTS.APPOINTMENTS.DELETE}/${id}`),
        getDoctors: (params) => api.get(API_ENDPOINTS.APPOINTMENTS.DOCTORS, params),
        getAvailability: (params) => api.get(API_ENDPOINTS.APPOINTMENTS.AVAILABILITY, params),
    },

    // Baby Care
    babyCare: {
        getInfo: (params) => api.get(API_ENDPOINTS.BABY_CARE.INFO, params),
        getSleepPatterns: (params) => api.get(API_ENDPOINTS.BABY_CARE.SLEEP_PATTERNS, params),
        getVaccinations: (params) => api.get(API_ENDPOINTS.BABY_CARE.VACCINATIONS, params),
        getMilestones: (params) => api.get(API_ENDPOINTS.BABY_CARE.MILESTONES, params),
        getGrowthCharts: (params) => api.get(API_ENDPOINTS.BABY_CARE.GROWTH_CHARTS, params),
    },

    // File Upload
    upload: {
        image: (file, data) => api.uploadFile(API_ENDPOINTS.UPLOAD.IMAGE, file, data),
        document: (file, data) => api.uploadFile(API_ENDPOINTS.UPLOAD.DOCUMENT, file, data),
        avatar: (file) => api.uploadFile(API_ENDPOINTS.UPLOAD.AVATAR, file),
    },

    // Health Check
    health: () => api.get(API_ENDPOINTS.HEALTH),
    info: () => api.get(API_ENDPOINTS.INFO),
};

// Utility functions
const ApiUtils = {
    /**
     * Handle API errors and show user-friendly messages
     */
    handleError(error, showNotification = true) {
        console.error('API Error:', error);

        let message = ERROR_MESSAGES.GENERIC_ERROR;

        if (error.message) {
            message = error.message;
        } else if (error.status) {
            switch (error.status) {
                case 400:
                    message = ERROR_MESSAGES.VALIDATION_ERROR;
                    break;
                case 401:
                    message = ERROR_MESSAGES.UNAUTHORIZED;
                    break;
                case 403:
                    message = ERROR_MESSAGES.FORBIDDEN;
                    break;
                case 404:
                    message = ERROR_MESSAGES.NOT_FOUND;
                    break;
                case 500:
                    message = ERROR_MESSAGES.SERVER_ERROR;
                    break;
                default:
                    message = ERROR_MESSAGES.GENERIC_ERROR;
            }
        }

        if (showNotification && window.showNotification) {
            window.showNotification(message, 'error');
        }

        return message;
    },

    /**
     * Show success message
     */
    handleSuccess(message, showNotification = true) {
        if (showNotification && window.showNotification) {
            window.showNotification(message, 'success');
        }
    },

    /**
     * Check if user is authenticated
     */
    isAuthenticated() {
        return !!api.getAuthToken();
    },

    /**
     * Get current user data from localStorage
     */
    getCurrentUser() {
        const userData = localStorage.getItem(APP_CONFIG.STORAGE_KEYS.USER_DATA);
        return userData ? JSON.parse(userData) : null;
    },

    /**
     * Set current user data in localStorage
     */
    setCurrentUser(userData) {
        if (userData) {
            localStorage.setItem(APP_CONFIG.STORAGE_KEYS.USER_DATA, JSON.stringify(userData));
        } else {
            localStorage.removeItem(APP_CONFIG.STORAGE_KEYS.USER_DATA);
        }
    },
};

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { ApiClient, ApiService, ApiUtils };
} else {
    window.ApiClient = ApiClient;
    window.ApiService = ApiService;
    window.ApiUtils = ApiUtils;
    window.api = api;
}
