/**
 * Authentication utilities for Preg and Baby Care application
 */

class AuthManager {
    constructor() {
        this.token = localStorage.getItem(CONFIG.STORAGE_KEYS.AUTH_TOKEN);
        this.user = this.getStoredUser();
        this.refreshTimer = null;
        
        // Initialize auth state
        this.initializeAuth();
    }

    /**
     * Initialize authentication state
     */
    initializeAuth() {
        if (this.token && this.user) {
            this.validateToken();
        } else {
            this.clearAuth();
        }
    }

    /**
     * Login user with email and password
     */
    async login(email, password) {
        try {
            const response = await ApiService.auth.login({ email, password });
            
            if (response.success) {
                this.setAuth(response.data.token, response.data.user);
                return { success: true, user: response.data.user };
            } else {
                return { success: false, error: response.error };
            }
        } catch (error) {
            console.error('Login error:', error);
            return { success: false, error: '<PERSON><PERSON> failed. Please try again.' };
        }
    }

    /**
     * Register new user
     */
    async register(userData) {
        try {
            const response = await ApiService.auth.register(userData);
            
            if (response.success) {
                this.setAuth(response.data.token, response.data.user);
                return { success: true, user: response.data.user };
            } else {
                return { success: false, error: response.error };
            }
        } catch (error) {
            console.error('Registration error:', error);
            return { success: false, error: 'Registration failed. Please try again.' };
        }
    }

    /**
     * Logout user
     */
    async logout() {
        try {
            if (this.token) {
                await ApiService.auth.logout();
            }
        } catch (error) {
            console.error('Logout error:', error);
        } finally {
            this.clearAuth();
            window.location.href = '/frontend/pages/login.html';
        }
    }

    /**
     * Refresh authentication token
     */
    async refreshToken() {
        try {
            const response = await ApiService.auth.refresh();
            
            if (response.success) {
                this.setAuth(response.data.token, response.data.user);
                return true;
            } else {
                this.clearAuth();
                return false;
            }
        } catch (error) {
            console.error('Token refresh error:', error);
            this.clearAuth();
            return false;
        }
    }

    /**
     * Validate current token
     */
    async validateToken() {
        if (!this.token) {
            this.clearAuth();
            return false;
        }

        try {
            const response = await ApiService.auth.validate();
            
            if (response.success) {
                this.user = response.data.user;
                this.updateStoredUser(this.user);
                this.scheduleTokenRefresh();
                return true;
            } else {
                this.clearAuth();
                return false;
            }
        } catch (error) {
            console.error('Token validation error:', error);
            this.clearAuth();
            return false;
        }
    }

    /**
     * Set authentication data
     */
    setAuth(token, user) {
        this.token = token;
        this.user = user;
        
        localStorage.setItem(CONFIG.STORAGE_KEYS.AUTH_TOKEN, token);
        localStorage.setItem(CONFIG.STORAGE_KEYS.USER_DATA, JSON.stringify(user));
        
        this.scheduleTokenRefresh();
        this.updateAuthUI();
    }

    /**
     * Clear authentication data
     */
    clearAuth() {
        this.token = null;
        this.user = null;
        
        localStorage.removeItem(CONFIG.STORAGE_KEYS.AUTH_TOKEN);
        localStorage.removeItem(CONFIG.STORAGE_KEYS.USER_DATA);
        
        if (this.refreshTimer) {
            clearTimeout(this.refreshTimer);
            this.refreshTimer = null;
        }
        
        this.updateAuthUI();
    }

    /**
     * Get stored user data
     */
    getStoredUser() {
        try {
            const userData = localStorage.getItem(CONFIG.STORAGE_KEYS.USER_DATA);
            return userData ? JSON.parse(userData) : null;
        } catch (error) {
            console.error('Error parsing stored user data:', error);
            return null;
        }
    }

    /**
     * Update stored user data
     */
    updateStoredUser(user) {
        this.user = user;
        localStorage.setItem(CONFIG.STORAGE_KEYS.USER_DATA, JSON.stringify(user));
        this.updateAuthUI();
    }

    /**
     * Schedule token refresh
     */
    scheduleTokenRefresh() {
        if (this.refreshTimer) {
            clearTimeout(this.refreshTimer);
        }
        
        // Refresh token 5 minutes before expiry (assuming 1 hour token life)
        const refreshTime = 55 * 60 * 1000; // 55 minutes
        this.refreshTimer = setTimeout(() => {
            this.refreshToken();
        }, refreshTime);
    }

    /**
     * Update authentication UI elements
     */
    updateAuthUI() {
        const navAuth = document.getElementById('nav-auth');
        if (!navAuth) return;

        if (this.isAuthenticated()) {
            navAuth.innerHTML = `
                <div class="user-menu">
                    <div class="user-info">
                        <span class="user-name">${this.user.name}</span>
                        <div class="user-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                    </div>
                    <div class="user-dropdown">
                        <a href="#" class="dropdown-item" onclick="authManager.viewProfile()">
                            <i class="fas fa-user"></i>
                            Profile
                        </a>
                        <a href="#" class="dropdown-item" onclick="authManager.viewSettings()">
                            <i class="fas fa-cog"></i>
                            Settings
                        </a>
                        <div class="dropdown-divider"></div>
                        <a href="#" class="dropdown-item" onclick="authManager.logout()">
                            <i class="fas fa-sign-out-alt"></i>
                            Logout
                        </a>
                    </div>
                </div>
            `;
        } else {
            navAuth.innerHTML = `
                <div class="auth-buttons">
                    <a href="login.html" class="btn btn-outline">Login</a>
                    <a href="signup.html" class="btn btn-primary">Sign Up</a>
                </div>
            `;
        }
    }

    /**
     * Check if user is authenticated
     */
    isAuthenticated() {
        return !!(this.token && this.user);
    }

    /**
     * Check if user has specific role
     */
    hasRole(role) {
        return this.user && this.user.role === role;
    }

    /**
     * Check if user is admin
     */
    isAdmin() {
        return this.hasRole('admin');
    }

    /**
     * Get current user
     */
    getCurrentUser() {
        return this.user;
    }

    /**
     * Get authentication token
     */
    getToken() {
        return this.token;
    }

    /**
     * Require authentication for page access
     */
    requireAuth() {
        if (!this.isAuthenticated()) {
            const currentPath = window.location.pathname;
            const loginUrl = `/frontend/pages/login.html?redirect=${encodeURIComponent(currentPath)}`;
            window.location.href = loginUrl;
            return false;
        }
        return true;
    }

    /**
     * Require admin role for page access
     */
    requireAdmin() {
        if (!this.requireAuth()) return false;
        
        if (!this.isAdmin()) {
            alert('Access denied. Admin privileges required.');
            window.location.href = '/frontend/index.html';
            return false;
        }
        return true;
    }

    /**
     * View user profile
     */
    viewProfile() {
        // Implement profile view functionality
        console.log('View profile clicked');
    }

    /**
     * View user settings
     */
    viewSettings() {
        // Implement settings view functionality
        console.log('View settings clicked');
    }

    /**
     * Update user profile
     */
    async updateProfile(profileData) {
        try {
            const response = await ApiService.auth.updateProfile(profileData);
            
            if (response.success) {
                this.updateStoredUser(response.data.user);
                return { success: true, user: response.data.user };
            } else {
                return { success: false, error: response.error };
            }
        } catch (error) {
            console.error('Profile update error:', error);
            return { success: false, error: 'Profile update failed. Please try again.' };
        }
    }

    /**
     * Change user password
     */
    async changePassword(currentPassword, newPassword) {
        try {
            const response = await ApiService.auth.changePassword({
                current_password: currentPassword,
                new_password: newPassword
            });
            
            if (response.success) {
                return { success: true };
            } else {
                return { success: false, error: response.error };
            }
        } catch (error) {
            console.error('Password change error:', error);
            return { success: false, error: 'Password change failed. Please try again.' };
        }
    }

    /**
     * Request password reset
     */
    async requestPasswordReset(email) {
        try {
            const response = await ApiService.auth.requestPasswordReset({ email });
            
            if (response.success) {
                return { success: true };
            } else {
                return { success: false, error: response.error };
            }
        } catch (error) {
            console.error('Password reset request error:', error);
            return { success: false, error: 'Password reset request failed. Please try again.' };
        }
    }

    /**
     * Reset password with token
     */
    async resetPassword(token, newPassword) {
        try {
            const response = await ApiService.auth.resetPassword({
                token,
                new_password: newPassword
            });
            
            if (response.success) {
                return { success: true };
            } else {
                return { success: false, error: response.error };
            }
        } catch (error) {
            console.error('Password reset error:', error);
            return { success: false, error: 'Password reset failed. Please try again.' };
        }
    }
}

// Create global auth manager instance
const authManager = new AuthManager();

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { AuthManager, authManager };
}
