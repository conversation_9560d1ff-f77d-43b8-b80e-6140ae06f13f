<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign Up - Preg and Baby Care</title>
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="../css/components.css">
    <link rel="stylesheet" href="../css/responsive.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="auth-page">
    <!-- Navigation -->
    <nav class="navbar navbar-minimal">
        <div class="nav-container">
            <div class="nav-brand">
                <a href="../index.html" class="brand-link">
                    <i class="fas fa-baby brand-icon"></i>
                    <span class="brand-text">Preg and Baby Care</span>
                </a>
            </div>
            
            <div class="nav-auth">
                <a href="login.html" class="btn btn-outline">
                    <i class="fas fa-sign-in-alt"></i>
                    Sign In
                </a>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="auth-main">
        <div class="auth-container">
            <div class="auth-card">
                <div class="auth-header">
                    <h1 class="auth-title">Join Our Community</h1>
                    <p class="auth-subtitle">Create your account to start your pregnancy and baby care journey</p>
                </div>

                <form class="auth-form" id="signup-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="firstName" class="form-label">
                                <i class="fas fa-user"></i>
                                First Name
                            </label>
                            <input 
                                type="text" 
                                id="firstName" 
                                name="firstName" 
                                class="form-input" 
                                placeholder="Enter your first name"
                                required
                                autocomplete="given-name"
                            >
                            <div class="form-error" id="firstName-error"></div>
                        </div>

                        <div class="form-group">
                            <label for="lastName" class="form-label">
                                <i class="fas fa-user"></i>
                                Last Name
                            </label>
                            <input 
                                type="text" 
                                id="lastName" 
                                name="lastName" 
                                class="form-input" 
                                placeholder="Enter your last name"
                                required
                                autocomplete="family-name"
                            >
                            <div class="form-error" id="lastName-error"></div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="email" class="form-label">
                            <i class="fas fa-envelope"></i>
                            Email Address
                        </label>
                        <input 
                            type="email" 
                            id="email" 
                            name="email" 
                            class="form-input" 
                            placeholder="Enter your email"
                            required
                            autocomplete="email"
                        >
                        <div class="form-error" id="email-error"></div>
                    </div>

                    <div class="form-group">
                        <label for="password" class="form-label">
                            <i class="fas fa-lock"></i>
                            Password
                        </label>
                        <div class="password-input-container">
                            <input 
                                type="password" 
                                id="password" 
                                name="password" 
                                class="form-input" 
                                placeholder="Create a strong password"
                                required
                                autocomplete="new-password"
                            >
                            <button type="button" class="password-toggle" id="password-toggle">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        <div class="password-strength" id="password-strength"></div>
                        <div class="form-error" id="password-error"></div>
                    </div>

                    <div class="form-group">
                        <label for="confirmPassword" class="form-label">
                            <i class="fas fa-lock"></i>
                            Confirm Password
                        </label>
                        <input 
                            type="password" 
                            id="confirmPassword" 
                            name="confirmPassword" 
                            class="form-input" 
                            placeholder="Confirm your password"
                            required
                            autocomplete="new-password"
                        >
                        <div class="form-error" id="confirmPassword-error"></div>
                    </div>

                    <div class="form-group">
                        <label for="phone" class="form-label">
                            <i class="fas fa-phone"></i>
                            Phone Number (Optional)
                        </label>
                        <input 
                            type="tel" 
                            id="phone" 
                            name="phone" 
                            class="form-input" 
                            placeholder="Enter your phone number"
                            autocomplete="tel"
                        >
                        <div class="form-error" id="phone-error"></div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-heart"></i>
                            Current Status
                        </label>
                        <div class="radio-group">
                            <label class="radio-label">
                                <input type="radio" name="status" value="pregnant" id="status-pregnant">
                                <span class="radio-custom"></span>
                                I'm currently pregnant
                            </label>
                            <label class="radio-label">
                                <input type="radio" name="status" value="planning" id="status-planning">
                                <span class="radio-custom"></span>
                                Planning to get pregnant
                            </label>
                            <label class="radio-label">
                                <input type="radio" name="status" value="parent" id="status-parent">
                                <span class="radio-custom"></span>
                                I have a baby/child
                            </label>
                        </div>
                    </div>

                    <div class="form-group" id="due-date-group" style="display: none;">
                        <label for="dueDate" class="form-label">
                            <i class="fas fa-calendar"></i>
                            Expected Due Date
                        </label>
                        <input 
                            type="date" 
                            id="dueDate" 
                            name="dueDate" 
                            class="form-input"
                        >
                        <div class="form-error" id="dueDate-error"></div>
                    </div>

                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="terms" name="terms" required>
                            <span class="checkbox-custom"></span>
                            I agree to the <a href="terms-of-service.html" target="_blank">Terms of Service</a> 
                            and <a href="privacy-policy.html" target="_blank">Privacy Policy</a>
                        </label>
                        <div class="form-error" id="terms-error"></div>
                    </div>

                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="newsletter" name="newsletter">
                            <span class="checkbox-custom"></span>
                            Subscribe to our newsletter for tips and updates
                        </label>
                    </div>

                    <button type="submit" class="btn btn-primary btn-full" id="signup-btn">
                        <i class="fas fa-user-plus"></i>
                        <span class="btn-text">Create Account</span>
                        <div class="btn-loading" style="display: none;">
                            <i class="fas fa-spinner fa-spin"></i>
                        </div>
                    </button>

                    <div class="form-error" id="form-error"></div>
                    <div class="form-success" id="form-success"></div>
                </form>

                <div class="auth-divider">
                    <span>or</span>
                </div>

                <div class="social-login">
                    <button class="btn btn-social btn-google" id="google-signup">
                        <i class="fab fa-google"></i>
                        Sign up with Google
                    </button>
                    <button class="btn btn-social btn-facebook" id="facebook-signup">
                        <i class="fab fa-facebook-f"></i>
                        Sign up with Facebook
                    </button>
                </div>

                <div class="auth-footer">
                    <p>Already have an account? 
                        <a href="login.html" class="auth-link">Sign in here</a>
                    </p>
                </div>
            </div>

            <div class="auth-benefits">
                <h3>What You'll Get</h3>
                <div class="benefits-list">
                    <div class="benefit-item">
                        <i class="fas fa-check-circle"></i>
                        <div>
                            <h4>Personalized Experience</h4>
                            <p>Tailored content based on your pregnancy stage or baby's age</p>
                        </div>
                    </div>
                    <div class="benefit-item">
                        <i class="fas fa-check-circle"></i>
                        <div>
                            <h4>Expert Guidance</h4>
                            <p>Access to evidence-based information and professional advice</p>
                        </div>
                    </div>
                    <div class="benefit-item">
                        <i class="fas fa-check-circle"></i>
                        <div>
                            <h4>Community Support</h4>
                            <p>Connect with other parents and share experiences</p>
                        </div>
                    </div>
                    <div class="benefit-item">
                        <i class="fas fa-check-circle"></i>
                        <div>
                            <h4>Progress Tracking</h4>
                            <p>Monitor your journey with comprehensive tracking tools</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer footer-minimal">
        <div class="container">
            <div class="footer-content">
                <div class="footer-links">
                    <a href="privacy-policy.html">Privacy Policy</a>
                    <a href="terms-of-service.html">Terms of Service</a>
                    <a href="contact.html">Contact</a>
                </div>
                <p>&copy; 2024 Preg and Baby Care. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript Files -->
    <script src="../js/utils/config.js"></script>
    <script src="../js/utils/api.js"></script>
    <script src="../js/utils/auth.js"></script>
    <script src="../js/pages/signup.js"></script>
</body>
</html>
