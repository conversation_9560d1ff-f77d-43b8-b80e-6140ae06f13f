/**
 * Navigation component for Preg and Baby Care application
 */

class NavbarManager {
    constructor() {
        this.navbar = null;
        this.navToggle = null;
        this.navMenu = null;
        this.dropdowns = [];
        this.isMenuOpen = false;
        
        this.init();
    }

    /**
     * Initialize navbar functionality
     */
    init() {
        this.navbar = document.getElementById('navbar');
        this.navToggle = document.getElementById('nav-toggle');
        this.navMenu = document.getElementById('nav-menu');
        
        if (!this.navbar) return;
        
        this.setupEventListeners();
        this.setupDropdowns();
        this.setupScrollBehavior();
        this.updateAuthState();
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Mobile menu toggle
        if (this.navToggle) {
            this.navToggle.addEventListener('click', (e) => {
                e.preventDefault();
                this.toggleMobileMenu();
            });
        }

        // Close menu when clicking outside
        document.addEventListener('click', (e) => {
            if (!this.navbar.contains(e.target)) {
                this.closeMobileMenu();
                this.closeAllDropdowns();
            }
        });

        // Handle window resize
        window.addEventListener('resize', () => {
            if (window.innerWidth > 768) {
                this.closeMobileMenu();
            }
        });

        // Handle escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeMobileMenu();
                this.closeAllDropdowns();
            }
        });
    }

    /**
     * Setup dropdown functionality
     */
    setupDropdowns() {
        const dropdownToggles = this.navbar.querySelectorAll('.dropdown-toggle');
        
        dropdownToggles.forEach(toggle => {
            const dropdown = toggle.parentElement;
            const menu = dropdown.querySelector('.dropdown-menu');
            
            if (menu) {
                this.dropdowns.push({ toggle, dropdown, menu });
                
                // Desktop hover behavior
                dropdown.addEventListener('mouseenter', () => {
                    if (window.innerWidth > 768) {
                        this.openDropdown(dropdown);
                    }
                });
                
                dropdown.addEventListener('mouseleave', () => {
                    if (window.innerWidth > 768) {
                        this.closeDropdown(dropdown);
                    }
                });
                
                // Mobile click behavior
                toggle.addEventListener('click', (e) => {
                    e.preventDefault();
                    if (window.innerWidth <= 768) {
                        this.toggleDropdown(dropdown);
                    }
                });
            }
        });
    }

    /**
     * Setup scroll behavior for navbar
     */
    setupScrollBehavior() {
        let lastScrollTop = 0;
        let scrollTimeout;
        
        window.addEventListener('scroll', () => {
            clearTimeout(scrollTimeout);
            
            scrollTimeout = setTimeout(() => {
                const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                
                // Add/remove scrolled class
                if (scrollTop > 50) {
                    this.navbar.classList.add('scrolled');
                } else {
                    this.navbar.classList.remove('scrolled');
                }
                
                // Hide/show navbar on scroll (optional)
                if (scrollTop > lastScrollTop && scrollTop > 100) {
                    this.navbar.classList.add('nav-hidden');
                } else {
                    this.navbar.classList.remove('nav-hidden');
                }
                
                lastScrollTop = scrollTop;
            }, 10);
        });
    }

    /**
     * Toggle mobile menu
     */
    toggleMobileMenu() {
        this.isMenuOpen = !this.isMenuOpen;
        
        if (this.isMenuOpen) {
            this.openMobileMenu();
        } else {
            this.closeMobileMenu();
        }
    }

    /**
     * Open mobile menu
     */
    openMobileMenu() {
        this.isMenuOpen = true;
        this.navMenu.classList.add('active');
        this.navToggle.classList.add('active');
        document.body.classList.add('nav-open');
        
        // Animate menu items
        const menuItems = this.navMenu.querySelectorAll('.nav-link, .dropdown-item');
        menuItems.forEach((item, index) => {
            item.style.animationDelay = `${index * 0.1}s`;
            item.classList.add('animate-in');
        });
    }

    /**
     * Close mobile menu
     */
    closeMobileMenu() {
        this.isMenuOpen = false;
        this.navMenu.classList.remove('active');
        this.navToggle.classList.remove('active');
        document.body.classList.remove('nav-open');
        
        // Remove animation classes
        const menuItems = this.navMenu.querySelectorAll('.nav-link, .dropdown-item');
        menuItems.forEach(item => {
            item.classList.remove('animate-in');
            item.style.animationDelay = '';
        });
        
        this.closeAllDropdowns();
    }

    /**
     * Toggle dropdown menu
     */
    toggleDropdown(dropdown) {
        const isOpen = dropdown.classList.contains('active');
        
        // Close all other dropdowns
        this.closeAllDropdowns();
        
        if (!isOpen) {
            this.openDropdown(dropdown);
        }
    }

    /**
     * Open dropdown menu
     */
    openDropdown(dropdown) {
        dropdown.classList.add('active');
        
        const menu = dropdown.querySelector('.dropdown-menu');
        if (menu) {
            menu.style.maxHeight = menu.scrollHeight + 'px';
        }
    }

    /**
     * Close dropdown menu
     */
    closeDropdown(dropdown) {
        dropdown.classList.remove('active');
        
        const menu = dropdown.querySelector('.dropdown-menu');
        if (menu) {
            menu.style.maxHeight = '';
        }
    }

    /**
     * Close all dropdown menus
     */
    closeAllDropdowns() {
        this.dropdowns.forEach(({ dropdown }) => {
            this.closeDropdown(dropdown);
        });
    }

    /**
     * Update authentication state in navbar
     */
    updateAuthState() {
        // This will be called by the auth manager
        // The auth manager handles updating the nav-auth element
    }

    /**
     * Highlight active page in navigation
     */
    highlightActivePage() {
        const currentPath = window.location.pathname;
        const navLinks = this.navbar.querySelectorAll('.nav-link, .dropdown-item');
        
        navLinks.forEach(link => {
            link.classList.remove('active');
            
            const href = link.getAttribute('href');
            if (href && currentPath.includes(href)) {
                link.classList.add('active');
                
                // Also highlight parent dropdown if this is a dropdown item
                const parentDropdown = link.closest('.nav-dropdown');
                if (parentDropdown) {
                    const parentToggle = parentDropdown.querySelector('.dropdown-toggle');
                    if (parentToggle) {
                        parentToggle.classList.add('active');
                    }
                }
            }
        });
    }

    /**
     * Add notification badge to navigation item
     */
    addNotificationBadge(selector, count) {
        const element = this.navbar.querySelector(selector);
        if (!element) return;
        
        // Remove existing badge
        const existingBadge = element.querySelector('.notification-badge');
        if (existingBadge) {
            existingBadge.remove();
        }
        
        // Add new badge if count > 0
        if (count > 0) {
            const badge = document.createElement('span');
            badge.className = 'notification-badge';
            badge.textContent = count > 99 ? '99+' : count.toString();
            element.appendChild(badge);
        }
    }

    /**
     * Remove notification badge from navigation item
     */
    removeNotificationBadge(selector) {
        const element = this.navbar.querySelector(selector);
        if (!element) return;
        
        const badge = element.querySelector('.notification-badge');
        if (badge) {
            badge.remove();
        }
    }

    /**
     * Show loading state in navigation
     */
    showLoading() {
        this.navbar.classList.add('loading');
    }

    /**
     * Hide loading state in navigation
     */
    hideLoading() {
        this.navbar.classList.remove('loading');
    }

    /**
     * Update user menu with user data
     */
    updateUserMenu(user) {
        const userMenu = this.navbar.querySelector('.user-menu');
        if (!userMenu || !user) return;
        
        const userName = userMenu.querySelector('.user-name');
        if (userName) {
            userName.textContent = user.name;
        }
        
        const userAvatar = userMenu.querySelector('.user-avatar');
        if (userAvatar && user.avatar) {
            userAvatar.innerHTML = `<img src="${user.avatar}" alt="${user.name}" class="avatar-img">`;
        }
    }

    /**
     * Add search functionality to navbar
     */
    addSearchFunctionality() {
        const searchContainer = document.createElement('div');
        searchContainer.className = 'nav-search';
        searchContainer.innerHTML = `
            <div class="search-input-container">
                <input type="text" class="search-input" placeholder="Search...">
                <button class="search-btn">
                    <i class="fas fa-search"></i>
                </button>
            </div>
            <div class="search-results" id="search-results"></div>
        `;
        
        const navSection = this.navbar.querySelector('.nav-section');
        if (navSection) {
            navSection.appendChild(searchContainer);
            this.setupSearchFunctionality(searchContainer);
        }
    }

    /**
     * Setup search functionality
     */
    setupSearchFunctionality(searchContainer) {
        const searchInput = searchContainer.querySelector('.search-input');
        const searchBtn = searchContainer.querySelector('.search-btn');
        const searchResults = searchContainer.querySelector('.search-results');
        
        let searchTimeout;
        
        const performSearch = async (query) => {
            if (query.length < 2) {
                searchResults.style.display = 'none';
                return;
            }
            
            try {
                // Implement search API call here
                const results = await this.searchContent(query);
                this.displaySearchResults(results, searchResults);
            } catch (error) {
                console.error('Search error:', error);
            }
        };
        
        searchInput.addEventListener('input', (e) => {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                performSearch(e.target.value.trim());
            }, 300);
        });
        
        searchBtn.addEventListener('click', () => {
            performSearch(searchInput.value.trim());
        });
        
        // Close search results when clicking outside
        document.addEventListener('click', (e) => {
            if (!searchContainer.contains(e.target)) {
                searchResults.style.display = 'none';
            }
        });
    }

    /**
     * Search content (placeholder implementation)
     */
    async searchContent(query) {
        // This would typically make an API call to search content
        // For now, return mock results
        return [
            { title: 'Pregnancy Nutrition', url: '/pages/nutrition.html', type: 'page' },
            { title: 'Baby Care Guide', url: '/pages/baby-care.html', type: 'page' },
            { title: 'Exercise During Pregnancy', url: '/pages/exercise.html', type: 'page' }
        ].filter(item => 
            item.title.toLowerCase().includes(query.toLowerCase())
        );
    }

    /**
     * Display search results
     */
    displaySearchResults(results, container) {
        if (results.length === 0) {
            container.innerHTML = '<div class="search-no-results">No results found</div>';
        } else {
            container.innerHTML = results.map(result => `
                <a href="${result.url}" class="search-result-item">
                    <div class="result-title">${result.title}</div>
                    <div class="result-type">${result.type}</div>
                </a>
            `).join('');
        }
        
        container.style.display = 'block';
    }
}

// Initialize navbar when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.navbarManager = new NavbarManager();
    
    // Highlight active page
    window.navbarManager.highlightActivePage();
});

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = NavbarManager;
}
