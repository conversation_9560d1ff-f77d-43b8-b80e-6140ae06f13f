<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Weight Tracker - Preg and Baby Care</title>
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="../css/components.css">
    <link rel="stylesheet" href="../css/responsive.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar" id="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <a href="../index.html" class="brand-link">
                    <i class="fas fa-baby brand-icon"></i>
                    <span class="brand-text">Preg and Baby Care</span>
                </a>
            </div>
            
            <div class="nav-menu" id="nav-menu">
                <div class="nav-section">
                    <div class="nav-dropdown">
                        <a href="#" class="nav-link dropdown-toggle">
                            <i class="fas fa-pregnant"></i>
                            Pregnancy Care
                            <i class="fas fa-chevron-down"></i>
                        </a>
                        <div class="dropdown-menu">
                            <a href="pregnancy-care.html" class="dropdown-item">
                                <i class="fas fa-heart"></i>
                                Pregnancy Guide
                            </a>
                            <a href="nutrition.html" class="dropdown-item">
                                <i class="fas fa-apple-alt"></i>
                                Nutrition Plans
                            </a>
                            <a href="weight-tracker.html" class="dropdown-item">
                                <i class="fas fa-weight"></i>
                                Weight Tracker
                            </a>
                            <a href="exercise.html" class="dropdown-item">
                                <i class="fas fa-dumbbell"></i>
                                Exercise Guide
                            </a>
                        </div>
                    </div>
                    
                    <div class="nav-dropdown">
                        <a href="#" class="nav-link dropdown-toggle">
                            <i class="fas fa-baby"></i>
                            Baby Care
                            <i class="fas fa-chevron-down"></i>
                        </a>
                        <div class="dropdown-menu">
                            <a href="baby-care.html" class="dropdown-item">
                                <i class="fas fa-baby-carriage"></i>
                                Baby Guide
                            </a>
                            <a href="baby-nutrition.html" class="dropdown-item">
                                <i class="fas fa-baby"></i>
                                Baby Nutrition
                            </a>
                            <a href="sleep-patterns.html" class="dropdown-item">
                                <i class="fas fa-moon"></i>
                                Sleep Patterns
                            </a>
                            <a href="vaccinations.html" class="dropdown-item">
                                <i class="fas fa-syringe"></i>
                                Vaccinations
                            </a>
                        </div>
                    </div>
                    
                    <a href="appointments.html" class="nav-link">
                        <i class="fas fa-calendar-check"></i>
                        Appointments
                    </a>
                    
                    <a href="meal-plans.html" class="nav-link">
                        <i class="fas fa-utensils"></i>
                        Meal Plans
                    </a>
                </div>
                
                <div class="nav-auth" id="nav-auth">
                    <!-- Will be populated by JavaScript based on auth state -->
                </div>
            </div>
            
            <div class="nav-toggle" id="nav-toggle">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Hero Section -->
        <section class="page-hero">
            <div class="container">
                <div class="hero-content">
                    <h1 class="hero-title">
                        <i class="fas fa-weight"></i>
                        Weight Tracker
                    </h1>
                    <p class="hero-description">
                        Monitor your pregnancy weight gain and track your baby's expected growth week by week.
                    </p>
                </div>
            </div>
        </section>

        <!-- Weight Overview -->
        <section class="weight-overview">
            <div class="container">
                <div class="overview-grid">
                    <div class="overview-card">
                        <div class="card-icon">
                            <i class="fas fa-weight"></i>
                        </div>
                        <h3>Current Weight</h3>
                        <div class="weight-display">
                            <span class="weight-value" id="current-weight">--</span>
                            <span class="weight-unit">lbs</span>
                        </div>
                        <p class="weight-date" id="last-recorded">Last recorded: --</p>
                    </div>
                    
                    <div class="overview-card">
                        <div class="card-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <h3>Total Gain</h3>
                        <div class="weight-display">
                            <span class="weight-value" id="total-gain">--</span>
                            <span class="weight-unit">lbs</span>
                        </div>
                        <p class="weight-status" id="gain-status">--</p>
                    </div>
                    
                    <div class="overview-card">
                        <div class="card-icon">
                            <i class="fas fa-target"></i>
                        </div>
                        <h3>Recommended Range</h3>
                        <div class="weight-display">
                            <span class="weight-value" id="recommended-range">--</span>
                            <span class="weight-unit">lbs</span>
                        </div>
                        <p class="weight-info" id="range-info">Based on pre-pregnancy BMI</p>
                    </div>
                    
                    <div class="overview-card">
                        <div class="card-icon">
                            <i class="fas fa-baby"></i>
                        </div>
                        <h3>Baby's Weight</h3>
                        <div class="weight-display">
                            <span class="weight-value" id="baby-weight">--</span>
                            <span class="weight-unit">oz</span>
                        </div>
                        <p class="weight-info" id="baby-week">Week --</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Add Weight Record -->
        <section class="add-weight">
            <div class="container">
                <div class="add-weight-card">
                    <div class="card-header">
                        <h3><i class="fas fa-plus"></i> Record Your Weight</h3>
                    </div>
                    <form class="weight-form" id="weight-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="weight-input">Weight</label>
                                <div class="input-group">
                                    <input type="number" id="weight-input" name="weight" step="0.1" min="50" max="500" required>
                                    <select id="weight-unit" name="unit">
                                        <option value="lbs">lbs</option>
                                        <option value="kg">kg</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label for="date-input">Date</label>
                                <input type="date" id="date-input" name="date" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="notes-input">Notes (Optional)</label>
                                <input type="text" id="notes-input" name="notes" placeholder="Any notes about this measurement">
                            </div>
                        </div>
                        
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i>
                                Record Weight
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </section>

        <!-- Weight Chart -->
        <section class="weight-chart">
            <div class="container">
                <div class="chart-card">
                    <div class="chart-header">
                        <h3>Weight Progress Chart</h3>
                        <div class="chart-controls">
                            <button class="btn btn-outline btn-sm" onclick="toggleChartView('weight')">
                                <i class="fas fa-chart-line"></i>
                                Your Weight
                            </button>
                            <button class="btn btn-outline btn-sm" onclick="toggleChartView('baby')">
                                <i class="fas fa-baby"></i>
                                Baby Weight
                            </button>
                            <button class="btn btn-outline btn-sm" onclick="toggleChartView('both')">
                                <i class="fas fa-chart-area"></i>
                                Both
                            </button>
                        </div>
                    </div>
                    <div class="chart-container">
                        <canvas id="weight-chart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>
        </section>

        <!-- Weight History -->
        <section class="weight-history">
            <div class="container">
                <div class="history-card">
                    <div class="history-header">
                        <h3>Weight History</h3>
                        <div class="history-controls">
                            <button class="btn btn-outline btn-sm" onclick="exportWeightData()">
                                <i class="fas fa-download"></i>
                                Export Data
                            </button>
                        </div>
                    </div>
                    <div class="history-content">
                        <div class="history-table-container">
                            <table class="history-table" id="weight-history-table">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Week</th>
                                        <th>Weight</th>
                                        <th>Gain</th>
                                        <th>Notes</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="weight-history-body">
                                    <!-- Weight records will be populated here -->
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="empty-state" id="weight-empty-state" style="display: none;">
                            <i class="fas fa-weight"></i>
                            <h4>No Weight Records Yet</h4>
                            <p>Start tracking your pregnancy weight by recording your first measurement above.</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Weight Guidelines -->
        <section class="weight-guidelines">
            <div class="container">
                <h2 class="section-title">Weight Gain Guidelines</h2>
                
                <div class="guidelines-grid">
                    <div class="guideline-card">
                        <div class="bmi-category">
                            <h3>Underweight (BMI < 18.5)</h3>
                            <div class="recommended-gain">
                                <span class="gain-range">28-40 lbs</span>
                                <span class="gain-rate">1-1.3 lbs/week (2nd & 3rd trimester)</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="guideline-card">
                        <div class="bmi-category">
                            <h3>Normal Weight (BMI 18.5-24.9)</h3>
                            <div class="recommended-gain">
                                <span class="gain-range">25-35 lbs</span>
                                <span class="gain-rate">1 lb/week (2nd & 3rd trimester)</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="guideline-card">
                        <div class="bmi-category">
                            <h3>Overweight (BMI 25-29.9)</h3>
                            <div class="recommended-gain">
                                <span class="gain-range">15-25 lbs</span>
                                <span class="gain-rate">0.6 lb/week (2nd & 3rd trimester)</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="guideline-card">
                        <div class="bmi-category">
                            <h3>Obese (BMI ≥ 30)</h3>
                            <div class="recommended-gain">
                                <span class="gain-range">11-20 lbs</span>
                                <span class="gain-rate">0.5 lb/week (2nd & 3rd trimester)</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="guidelines-note">
                    <p><strong>Note:</strong> These are general guidelines. Always consult with your healthcare provider for personalized recommendations based on your individual health and pregnancy circumstances.</p>
                </div>
            </div>
        </section>

        <!-- Baby Weight Development -->
        <section class="baby-weight-development">
            <div class="container">
                <h2 class="section-title">Baby's Weight Development by Week</h2>
                
                <div class="development-timeline">
                    <div class="timeline-controls">
                        <button class="btn btn-outline btn-sm" onclick="showTrimester(1)">1st Trimester</button>
                        <button class="btn btn-outline btn-sm" onclick="showTrimester(2)">2nd Trimester</button>
                        <button class="btn btn-outline btn-sm" onclick="showTrimester(3)">3rd Trimester</button>
                    </div>
                    
                    <div class="timeline-content" id="baby-timeline">
                        <!-- Timeline will be populated by JavaScript -->
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Edit Weight Modal -->
    <div class="modal" id="edit-weight-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Edit Weight Record</h3>
                <button class="modal-close" onclick="closeEditModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="edit-weight-form">
                    <input type="hidden" id="edit-record-id">
                    <div class="form-group">
                        <label for="edit-weight">Weight</label>
                        <div class="input-group">
                            <input type="number" id="edit-weight" step="0.1" min="50" max="500" required>
                            <select id="edit-unit">
                                <option value="lbs">lbs</option>
                                <option value="kg">kg</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="edit-date">Date</label>
                        <input type="date" id="edit-date" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="edit-notes">Notes</label>
                        <input type="text" id="edit-notes" placeholder="Any notes about this measurement">
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" class="btn btn-outline" onclick="closeEditModal()">Cancel</button>
                        <button type="submit" class="btn btn-primary">Update Record</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="footer-brand">
                        <i class="fas fa-baby"></i>
                        <span>Preg and Baby Care</span>
                    </div>
                    <p class="footer-description">
                        Your trusted companion for pregnancy and baby care with AI-powered guidance and expert advice.
                    </p>
                </div>
                
                <div class="footer-section">
                    <h4 class="footer-title">Quick Links</h4>
                    <ul class="footer-links">
                        <li><a href="pregnancy-care.html">Pregnancy Guide</a></li>
                        <li><a href="baby-care.html">Baby Care</a></li>
                        <li><a href="nutrition.html">Nutrition</a></li>
                        <li><a href="appointments.html">Appointments</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4 class="footer-title">Support</h4>
                    <ul class="footer-links">
                        <li><a href="contact.html">Contact Us</a></li>
                        <li><a href="privacy-policy.html">Privacy Policy</a></li>
                        <li><a href="terms-of-service.html">Terms of Service</a></li>
                        <li><a href="cookie-policy.html">Cookie Policy</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4 class="footer-title">Connect</h4>
                    <div class="social-links">
                        <a href="#" class="social-link"><i class="fab fa-facebook"></i></a>
                        <a href="#" class="social-link"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="social-link"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="social-link"><i class="fab fa-youtube"></i></a>
                    </div>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; 2024 Preg and Baby Care. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Chatbot -->
    <div class="chatbot-container" id="chatbot-container">
        <!-- Will be populated by chatbot.js -->
    </div>

    <!-- Back to Top Button -->
    <button class="back-to-top" id="back-to-top" aria-label="Back to top">
        <i class="fas fa-arrow-up"></i>
    </button>

    <!-- JavaScript Files -->
    <script src="../js/utils/config.js"></script>
    <script src="../js/utils/api.js"></script>
    <script src="../js/utils/auth.js"></script>
    <script src="../js/components/navbar.js"></script>
    <script src="../js/components/chatbot.js"></script>
    <script src="../js/pages/weight-tracker.js"></script>
    <script src="../js/main.js"></script>
</body>
</html>
