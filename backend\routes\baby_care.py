#!/usr/bin/env python3
"""
Baby care routes for Preg and Baby Care application.
"""

from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from models.database import get_db_connection

baby_care_bp = Blueprint('baby_care', __name__)

@baby_care_bp.route('/info', methods=['GET'])
def get_baby_care_info():
    """Get baby care information."""
    try:
        category = request.args.get('category')
        age_months = request.args.get('age_months', type=int)
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        query = 'SELECT * FROM baby_care_info WHERE is_active = 1'
        params = []
        
        if category:
            query += ' AND category = ?'
            params.append(category)
        
        if age_months is not None:
            query += ' AND (age_range_start_months <= ? AND age_range_end_months >= ?)'
            params.extend([age_months, age_months])
        
        query += ' ORDER BY age_range_start_months, created_at DESC'
        
        cursor.execute(query, params)
        info = cursor.fetchall()
        conn.close()
        
        return jsonify({
            'baby_care_info': [dict(item) for item in info]
        }), 200
        
    except Exception as e:
        return jsonify({'error': f'Failed to get baby care info: {str(e)}'}), 500

@baby_care_bp.route('/sleep-patterns', methods=['GET'])
@jwt_required()
def get_sleep_patterns():
    """Get user's baby sleep patterns."""
    try:
        user_id = get_jwt_identity()
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT * FROM sleep_patterns 
            WHERE user_id = ? 
            ORDER BY recorded_date DESC
        ''', (user_id,))
        
        patterns = cursor.fetchall()
        conn.close()
        
        return jsonify({
            'sleep_patterns': [dict(pattern) for pattern in patterns]
        }), 200
        
    except Exception as e:
        return jsonify({'error': f'Failed to get sleep patterns: {str(e)}'}), 500

@baby_care_bp.route('/vaccinations', methods=['GET'])
@jwt_required()
def get_vaccinations():
    """Get user's vaccination records."""
    try:
        user_id = get_jwt_identity()
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT * FROM vaccination_records 
            WHERE user_id = ? 
            ORDER BY administered_date DESC
        ''', (user_id,))
        
        vaccinations = cursor.fetchall()
        conn.close()
        
        return jsonify({
            'vaccinations': [dict(vaccination) for vaccination in vaccinations]
        }), 200
        
    except Exception as e:
        return jsonify({'error': f'Failed to get vaccinations: {str(e)}'}), 500
