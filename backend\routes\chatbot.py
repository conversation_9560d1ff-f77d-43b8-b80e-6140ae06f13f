#!/usr/bin/env python3
"""
Chatbot routes for Preg and Baby Care application.
Integrates with Google Gemini AI for intelligent responses.
"""

from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity, verify_jwt_in_request
from services.gemini_service import GeminiService
from models.database import get_db_connection
import uuid
from datetime import datetime

chatbot_bp = Blueprint('chatbot', __name__)

@chatbot_bp.route('/chat', methods=['POST'])
def chat():
    """Handle chat messages with optional authentication."""
    try:
        # Check if user is authenticated (optional)
        user_id = None
        try:
            verify_jwt_in_request(optional=True)
            user_id = get_jwt_identity()
        except:
            pass  # Anonymous user
        
        data = request.get_json()
        message = data.get('message', '').strip()
        session_id = data.get('session_id', str(uuid.uuid4()))
        
        if not message:
            return jsonify({'error': 'Message is required'}), 400
        
        # Get user context if authenticated
        user_context = None
        if user_id:
            conn = get_db_connection()
            cursor = conn.cursor()
            cursor.execute('''
                SELECT u.first_name, u.is_pregnant, u.due_date, u.baby_birth_date,
                       p.height_cm, p.pre_pregnancy_weight_kg
                FROM users u
                LEFT JOIN user_profiles p ON u.id = p.user_id
                WHERE u.id = ?
            ''', (user_id,))
            user_data = cursor.fetchone()
            
            if user_data:
                user_context = {
                    'first_name': user_data['first_name'],
                    'is_pregnant': bool(user_data['is_pregnant']),
                    'due_date': user_data['due_date'],
                    'baby_birth_date': user_data['baby_birth_date'],
                    'height_cm': user_data['height_cm'],
                    'pre_pregnancy_weight_kg': user_data['pre_pregnancy_weight_kg']
                }
            conn.close()
        
        # Get chat history for context
        chat_history = []
        if user_id:
            conn = get_db_connection()
            cursor = conn.cursor()
            cursor.execute('''
                SELECT message_type, message_content 
                FROM chat_history 
                WHERE session_id = ? 
                ORDER BY created_at DESC 
                LIMIT 10
            ''', (session_id,))
            history = cursor.fetchall()
            chat_history = [{'role': h['message_type'], 'content': h['message_content']} for h in reversed(history)]
            conn.close()
        
        # Generate response using Gemini
        gemini_service = GeminiService()
        response = gemini_service.generate_response(message, user_context, chat_history)
        
        # Save chat history
        if user_id:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # Save user message
            cursor.execute('''
                INSERT INTO chat_history (user_id, session_id, message_type, message_content)
                VALUES (?, ?, ?, ?)
            ''', (user_id, session_id, 'user', message))
            
            # Save assistant response
            cursor.execute('''
                INSERT INTO chat_history (user_id, session_id, message_type, message_content)
                VALUES (?, ?, ?, ?)
            ''', (user_id, session_id, 'assistant', response['content']))
            
            conn.commit()
            conn.close()
        
        return jsonify({
            'response': response['content'],
            'session_id': session_id,
            'timestamp': datetime.now().isoformat(),
            'context_used': user_context is not None
        }), 200
        
    except Exception as e:
        return jsonify({'error': f'Chat failed: {str(e)}'}), 500

@chatbot_bp.route('/history', methods=['GET'])
@jwt_required()
def get_chat_history():
    """Get user's chat history."""
    try:
        user_id = get_jwt_identity()
        session_id = request.args.get('session_id')
        limit = request.args.get('limit', 50, type=int)
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        query = '''
            SELECT session_id, message_type, message_content, created_at
            FROM chat_history 
            WHERE user_id = ?
        '''
        params = [user_id]
        
        if session_id:
            query += ' AND session_id = ?'
            params.append(session_id)
        
        query += ' ORDER BY created_at DESC LIMIT ?'
        params.append(limit)
        
        cursor.execute(query, params)
        history = cursor.fetchall()
        conn.close()
        
        return jsonify({
            'history': [dict(h) for h in history]
        }), 200
        
    except Exception as e:
        return jsonify({'error': f'Failed to get chat history: {str(e)}'}), 500

@chatbot_bp.route('/sessions', methods=['GET'])
@jwt_required()
def get_chat_sessions():
    """Get user's chat sessions."""
    try:
        user_id = get_jwt_identity()
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT session_id, 
                   MIN(created_at) as first_message,
                   MAX(created_at) as last_message,
                   COUNT(*) as message_count
            FROM chat_history 
            WHERE user_id = ?
            GROUP BY session_id
            ORDER BY last_message DESC
        ''', (user_id,))
        
        sessions = cursor.fetchall()
        conn.close()
        
        return jsonify({
            'sessions': [dict(s) for s in sessions]
        }), 200
        
    except Exception as e:
        return jsonify({'error': f'Failed to get chat sessions: {str(e)}'}), 500

@chatbot_bp.route('/quick-questions', methods=['GET'])
def get_quick_questions():
    """Get predefined quick questions for the chatbot."""
    try:
        category = request.args.get('category', 'general')
        
        questions = {
            'pregnancy': [
                "What should I eat during my first trimester?",
                "How much weight should I gain during pregnancy?",
                "What exercises are safe during pregnancy?",
                "When should I call my doctor?",
                "What are the signs of labor?"
            ],
            'baby': [
                "When should I start solid foods?",
                "How often should my baby sleep?",
                "What are the vaccination schedules?",
                "How do I know if my baby is developing normally?",
                "What should I do if my baby has a fever?"
            ],
            'general': [
                "How can I track my pregnancy progress?",
                "What are the important prenatal appointments?",
                "How do I prepare for breastfeeding?",
                "What should I pack in my hospital bag?",
                "How do I choose a pediatrician?"
            ]
        }
        
        return jsonify({
            'category': category,
            'questions': questions.get(category, questions['general'])
        }), 200
        
    except Exception as e:
        return jsonify({'error': f'Failed to get quick questions: {str(e)}'}), 500

@chatbot_bp.route('/feedback', methods=['POST'])
@jwt_required()
def submit_feedback():
    """Submit feedback on chatbot responses."""
    try:
        user_id = get_jwt_identity()
        data = request.get_json()
        
        session_id = data.get('session_id')
        rating = data.get('rating')  # 1-5 scale
        feedback_text = data.get('feedback', '')
        
        if not session_id or not rating:
            return jsonify({'error': 'Session ID and rating are required'}), 400
        
        # For now, we'll just return success
        # In a real application, you'd save this to a feedback table
        
        return jsonify({
            'message': 'Feedback submitted successfully',
            'thank_you': 'Thank you for helping us improve!'
        }), 200
        
    except Exception as e:
        return jsonify({'error': f'Failed to submit feedback: {str(e)}'}), 500
