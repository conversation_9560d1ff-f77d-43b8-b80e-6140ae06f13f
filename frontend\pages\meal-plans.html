<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Meal Plans - Preg and Baby Care</title>
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="../css/components.css">
    <link rel="stylesheet" href="../css/responsive.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar" id="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <a href="../index.html" class="brand-link">
                    <i class="fas fa-baby brand-icon"></i>
                    <span class="brand-text">Preg and Baby Care</span>
                </a>
            </div>
            
            <div class="nav-menu" id="nav-menu">
                <div class="nav-section">
                    <div class="nav-dropdown">
                        <a href="#" class="nav-link dropdown-toggle">
                            <i class="fas fa-pregnant"></i>
                            Pregnancy Care
                            <i class="fas fa-chevron-down"></i>
                        </a>
                        <div class="dropdown-menu">
                            <a href="pregnancy-care.html" class="dropdown-item">
                                <i class="fas fa-heart"></i>
                                Pregnancy Guide
                            </a>
                            <a href="nutrition.html" class="dropdown-item">
                                <i class="fas fa-apple-alt"></i>
                                Nutrition Plans
                            </a>
                            <a href="weight-tracker.html" class="dropdown-item">
                                <i class="fas fa-weight"></i>
                                Weight Tracker
                            </a>
                            <a href="exercise.html" class="dropdown-item">
                                <i class="fas fa-dumbbell"></i>
                                Exercise Guide
                            </a>
                        </div>
                    </div>
                    
                    <div class="nav-dropdown">
                        <a href="#" class="nav-link dropdown-toggle">
                            <i class="fas fa-baby"></i>
                            Baby Care
                            <i class="fas fa-chevron-down"></i>
                        </a>
                        <div class="dropdown-menu">
                            <a href="baby-care.html" class="dropdown-item">
                                <i class="fas fa-baby-carriage"></i>
                                Baby Guide
                            </a>
                            <a href="baby-nutrition.html" class="dropdown-item">
                                <i class="fas fa-baby"></i>
                                Baby Nutrition
                            </a>
                            <a href="sleep-patterns.html" class="dropdown-item">
                                <i class="fas fa-moon"></i>
                                Sleep Patterns
                            </a>
                            <a href="vaccinations.html" class="dropdown-item">
                                <i class="fas fa-syringe"></i>
                                Vaccinations
                            </a>
                        </div>
                    </div>
                    
                    <a href="appointments.html" class="nav-link">
                        <i class="fas fa-calendar-check"></i>
                        Appointments
                    </a>
                    
                    <a href="meal-plans.html" class="nav-link">
                        <i class="fas fa-utensils"></i>
                        Meal Plans
                    </a>
                </div>
                
                <div class="nav-auth" id="nav-auth">
                    <!-- Will be populated by JavaScript based on auth state -->
                </div>
            </div>
            
            <div class="nav-toggle" id="nav-toggle">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Hero Section -->
        <section class="page-hero">
            <div class="container">
                <div class="hero-content">
                    <h1 class="hero-title">
                        <i class="fas fa-utensils"></i>
                        Daily Meal Plans
                    </h1>
                    <p class="hero-description">
                        Personalized meal plans that change daily to provide optimal nutrition throughout your pregnancy.
                    </p>
                </div>
            </div>
        </section>

        <!-- Today's Meal Plan -->
        <section class="todays-meal-plan">
            <div class="container">
                <div class="meal-plan-header">
                    <div class="date-display">
                        <h2 class="section-title">Today's Meal Plan</h2>
                        <p class="current-date" id="current-date">Loading...</p>
                    </div>
                    <div class="plan-controls">
                        <button class="btn btn-outline btn-sm" onclick="previousDay()">
                            <i class="fas fa-chevron-left"></i>
                            Previous Day
                        </button>
                        <button class="btn btn-outline btn-sm" onclick="nextDay()">
                            Next Day
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>
                
                <div class="daily-meal-grid" id="daily-meal-grid">
                    <!-- Meals will be loaded dynamically -->
                    <div class="loading-state" id="meals-loading">
                        <i class="fas fa-spinner fa-spin"></i>
                        <p>Loading today's meal plan...</p>
                    </div>
                </div>
                
                <div class="daily-nutrition-summary" id="nutrition-summary">
                    <h3>Daily Nutrition Summary</h3>
                    <div class="nutrition-grid">
                        <div class="nutrition-item">
                            <span class="label">Calories:</span>
                            <span class="value" id="total-calories">--</span>
                        </div>
                        <div class="nutrition-item">
                            <span class="label">Protein:</span>
                            <span class="value" id="total-protein">--</span>
                        </div>
                        <div class="nutrition-item">
                            <span class="label">Carbs:</span>
                            <span class="value" id="total-carbs">--</span>
                        </div>
                        <div class="nutrition-item">
                            <span class="label">Fat:</span>
                            <span class="value" id="total-fat">--</span>
                        </div>
                        <div class="nutrition-item">
                            <span class="label">Fiber:</span>
                            <span class="value" id="total-fiber">--</span>
                        </div>
                        <div class="nutrition-item">
                            <span class="label">Iron:</span>
                            <span class="value" id="total-iron">--</span>
                        </div>
                    </div>
                </div>
                
                <div class="daily-snacks" id="daily-snacks">
                    <h3>Recommended Snacks</h3>
                    <div class="snacks-grid" id="snacks-grid">
                        <!-- Snacks will be loaded dynamically -->
                    </div>
                </div>
            </div>
        </section>

        <!-- Weekly Overview -->
        <section class="weekly-overview">
            <div class="container">
                <div class="weekly-header">
                    <h2 class="section-title">This Week's Overview</h2>
                    <div class="week-navigation">
                        <button class="btn btn-outline btn-sm" onclick="previousWeek()">
                            <i class="fas fa-chevron-left"></i>
                            Previous Week
                        </button>
                        <span class="week-display" id="week-display">Loading...</span>
                        <button class="btn btn-outline btn-sm" onclick="nextWeek()">
                            Next Week
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>
                
                <div class="weekly-calendar" id="weekly-calendar">
                    <!-- Weekly meal plan calendar will be populated here -->
                </div>
            </div>
        </section>

        <!-- Meal Plan Features -->
        <section class="meal-plan-features">
            <div class="container">
                <h2 class="section-title">Meal Plan Features</h2>
                
                <div class="features-grid">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-calendar-alt"></i>
                        </div>
                        <h3>Daily Updates</h3>
                        <p>Fresh meal plans every day with variety and balanced nutrition for your pregnancy stage.</p>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-user-md"></i>
                        </div>
                        <h3>Expert Designed</h3>
                        <p>Created by nutritionists and pregnancy experts to meet your specific nutritional needs.</p>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <h3>Shopping Lists</h3>
                        <p>Automatically generated shopping lists based on your weekly meal plans.</p>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-heart"></i>
                        </div>
                        <h3>Favorites</h3>
                        <p>Save your favorite meals and recipes for easy access and meal planning.</p>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-allergies"></i>
                        </div>
                        <h3>Dietary Restrictions</h3>
                        <p>Customized plans that accommodate allergies, preferences, and dietary restrictions.</p>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-chart-pie"></i>
                        </div>
                        <h3>Nutrition Tracking</h3>
                        <p>Track your daily nutrition intake and ensure you're meeting pregnancy requirements.</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Quick Actions -->
        <section class="quick-actions">
            <div class="container">
                <h2 class="section-title">Meal Planning Tools</h2>
                <div class="actions-grid">
                    <button class="action-card" onclick="generateShoppingList()">
                        <div class="action-icon">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <h3>Generate Shopping List</h3>
                        <p>Create a shopping list for this week's meals</p>
                    </button>
                    
                    <button class="action-card" onclick="showFavorites()">
                        <div class="action-icon">
                            <i class="fas fa-heart"></i>
                        </div>
                        <h3>My Favorites</h3>
                        <p>View and manage your favorite meals</p>
                    </button>
                    
                    <button class="action-card" onclick="customizePlan()">
                        <div class="action-icon">
                            <i class="fas fa-cog"></i>
                        </div>
                        <h3>Customize Plan</h3>
                        <p>Adjust preferences and dietary restrictions</p>
                    </button>
                    
                    <a href="#" class="action-card" onclick="openChatbot()">
                        <div class="action-icon">
                            <i class="fas fa-robot"></i>
                        </div>
                        <h3>Ask AI Assistant</h3>
                        <p>Get meal planning advice and recipe suggestions</p>
                    </a>
                </div>
            </div>
        </section>

        <!-- Nutrition Tips -->
        <section class="nutrition-tips">
            <div class="container">
                <h2 class="section-title">Daily Nutrition Tips</h2>
                
                <div class="tips-carousel" id="nutrition-tips-carousel">
                    <div class="tip-card active">
                        <div class="tip-icon">
                            <i class="fas fa-tint"></i>
                        </div>
                        <h3>Stay Hydrated</h3>
                        <p>Drink at least 8-10 glasses of water daily. Proper hydration supports increased blood volume and helps prevent constipation.</p>
                    </div>
                    
                    <div class="tip-card">
                        <div class="tip-icon">
                            <i class="fas fa-apple-alt"></i>
                        </div>
                        <h3>Eat Small, Frequent Meals</h3>
                        <p>Eating smaller meals every 2-3 hours helps manage nausea and maintains steady blood sugar levels.</p>
                    </div>
                    
                    <div class="tip-card">
                        <div class="tip-icon">
                            <i class="fas fa-leaf"></i>
                        </div>
                        <h3>Focus on Folate</h3>
                        <p>Include folate-rich foods like leafy greens, citrus fruits, and fortified cereals to support baby's neural development.</p>
                    </div>
                    
                    <div class="tip-card">
                        <div class="tip-icon">
                            <i class="fas fa-fish"></i>
                        </div>
                        <h3>Choose Safe Seafood</h3>
                        <p>Opt for low-mercury fish like salmon, sardines, and shrimp. Limit to 2-3 servings per week.</p>
                    </div>
                </div>
                
                <div class="carousel-controls">
                    <button class="carousel-btn" onclick="previousTip()">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <div class="carousel-dots" id="carousel-dots">
                        <!-- Dots will be generated dynamically -->
                    </div>
                    <button class="carousel-btn" onclick="nextTip()">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>
        </section>
    </main>

    <!-- Shopping List Modal -->
    <div class="modal" id="shopping-list-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Weekly Shopping List</h3>
                <button class="modal-close" onclick="closeShoppingListModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="shopping-list-content" id="shopping-list-content">
                    <!-- Shopping list will be populated here -->
                </div>
                <div class="modal-actions">
                    <button class="btn btn-outline" onclick="printShoppingList()">
                        <i class="fas fa-print"></i>
                        Print List
                    </button>
                    <button class="btn btn-primary" onclick="emailShoppingList()">
                        <i class="fas fa-envelope"></i>
                        Email List
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Favorites Modal -->
    <div class="modal" id="favorites-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>My Favorite Meals</h3>
                <button class="modal-close" onclick="closeFavoritesModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="favorites-content" id="favorites-content">
                    <!-- Favorites will be populated here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Customize Plan Modal -->
    <div class="modal" id="customize-plan-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Customize Your Meal Plan</h3>
                <button class="modal-close" onclick="closeCustomizeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="customize-plan-form">
                    <div class="form-section">
                        <h4>Dietary Preferences</h4>
                        <div class="checkbox-group">
                            <label class="checkbox-label">
                                <input type="checkbox" name="preferences" value="vegetarian">
                                <span class="checkmark"></span>
                                Vegetarian
                            </label>
                            <label class="checkbox-label">
                                <input type="checkbox" name="preferences" value="vegan">
                                <span class="checkmark"></span>
                                Vegan
                            </label>
                            <label class="checkbox-label">
                                <input type="checkbox" name="preferences" value="gluten-free">
                                <span class="checkmark"></span>
                                Gluten-Free
                            </label>
                            <label class="checkbox-label">
                                <input type="checkbox" name="preferences" value="dairy-free">
                                <span class="checkmark"></span>
                                Dairy-Free
                            </label>
                        </div>
                    </div>
                    
                    <div class="form-section">
                        <h4>Food Allergies</h4>
                        <div class="checkbox-group">
                            <label class="checkbox-label">
                                <input type="checkbox" name="allergies" value="nuts">
                                <span class="checkmark"></span>
                                Nuts
                            </label>
                            <label class="checkbox-label">
                                <input type="checkbox" name="allergies" value="shellfish">
                                <span class="checkmark"></span>
                                Shellfish
                            </label>
                            <label class="checkbox-label">
                                <input type="checkbox" name="allergies" value="eggs">
                                <span class="checkmark"></span>
                                Eggs
                            </label>
                            <label class="checkbox-label">
                                <input type="checkbox" name="allergies" value="soy">
                                <span class="checkmark"></span>
                                Soy
                            </label>
                        </div>
                    </div>
                    
                    <div class="form-section">
                        <h4>Meal Preferences</h4>
                        <div class="form-group">
                            <label for="meal-complexity">Meal Complexity</label>
                            <select id="meal-complexity" name="complexity">
                                <option value="simple">Simple (15-20 min prep)</option>
                                <option value="moderate">Moderate (30-45 min prep)</option>
                                <option value="complex">Complex (60+ min prep)</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="cuisine-preference">Cuisine Preference</label>
                            <select id="cuisine-preference" name="cuisine">
                                <option value="any">Any Cuisine</option>
                                <option value="american">American</option>
                                <option value="mediterranean">Mediterranean</option>
                                <option value="asian">Asian</option>
                                <option value="mexican">Mexican</option>
                                <option value="italian">Italian</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" class="btn btn-outline" onclick="closeCustomizeModal()">Cancel</button>
                        <button type="submit" class="btn btn-primary">Save Preferences</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="footer-brand">
                        <i class="fas fa-baby"></i>
                        <span>Preg and Baby Care</span>
                    </div>
                    <p class="footer-description">
                        Your trusted companion for pregnancy and baby care with AI-powered guidance and expert advice.
                    </p>
                </div>
                
                <div class="footer-section">
                    <h4 class="footer-title">Quick Links</h4>
                    <ul class="footer-links">
                        <li><a href="pregnancy-care.html">Pregnancy Guide</a></li>
                        <li><a href="baby-care.html">Baby Care</a></li>
                        <li><a href="nutrition.html">Nutrition</a></li>
                        <li><a href="appointments.html">Appointments</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4 class="footer-title">Support</h4>
                    <ul class="footer-links">
                        <li><a href="contact.html">Contact Us</a></li>
                        <li><a href="privacy-policy.html">Privacy Policy</a></li>
                        <li><a href="terms-of-service.html">Terms of Service</a></li>
                        <li><a href="cookie-policy.html">Cookie Policy</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4 class="footer-title">Connect</h4>
                    <div class="social-links">
                        <a href="#" class="social-link"><i class="fab fa-facebook"></i></a>
                        <a href="#" class="social-link"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="social-link"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="social-link"><i class="fab fa-youtube"></i></a>
                    </div>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; 2024 Preg and Baby Care. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Chatbot -->
    <div class="chatbot-container" id="chatbot-container">
        <!-- Will be populated by chatbot.js -->
    </div>

    <!-- Back to Top Button -->
    <button class="back-to-top" id="back-to-top" aria-label="Back to top">
        <i class="fas fa-arrow-up"></i>
    </button>

    <!-- JavaScript Files -->
    <script src="../js/utils/config.js"></script>
    <script src="../js/utils/api.js"></script>
    <script src="../js/utils/auth.js"></script>
    <script src="../js/components/navbar.js"></script>
    <script src="../js/components/chatbot.js"></script>
    <script src="../js/pages/meal-plans.js"></script>
    <script src="../js/main.js"></script>
</body>
</html>
