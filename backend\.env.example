# Security Configuration
SECRET_KEY=your_super_secret_key_change_in_production_please_make_it_very_long_and_random
JWT_SECRET_KEY=your_jwt_secret_key_also_make_this_very_long_and_random

# Google Gemini AI Configuration
GEMINI_API_KEY=your_gemini_api_key_here

# Database Configuration
DATABASE_URL=sqlite:///preg_baby_care.db

# Flask Configuration
FLASK_ENV=development
FLASK_DEBUG=True
HOST=127.0.0.1
PORT=5000

# CORS Configuration
CORS_ORIGINS=http://localhost:8000,http://127.0.0.1:8000

# JWT Configuration
JWT_ACCESS_TOKEN_EXPIRES=3600
JWT_REFRESH_TOKEN_EXPIRES=2592000

# Application Configuration
APP_NAME=Preg and Baby Care
APP_VERSION=1.0.0
