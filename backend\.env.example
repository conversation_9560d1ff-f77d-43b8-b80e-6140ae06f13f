# Security Configuration
SECRET_KEY=dev_secret_key_for_local_development_only
JWT_SECRET_KEY=dev_jwt_secret_key_for_local_development_only

# Google Gemini AI Configuration
GEMINI_API_KEY=your_gemini_api_key_here

# Database Configuration
DATABASE_URL=sqlite:///preg_baby_care.db

# Flask Configuration
FLASK_ENV=development
FLASK_DEBUG=True
HOST=127.0.0.1
PORT=5000

# CORS Configuration
CORS_ORIGINS=http://localhost:8000,http://127.0.0.1:8000

# JWT Configuration
JWT_ACCESS_TOKEN_EXPIRES=3600
JWT_REFRESH_TOKEN_EXPIRES=2592000

# Application Configuration
APP_NAME=Preg and Baby Care
APP_VERSION=1.0.0
