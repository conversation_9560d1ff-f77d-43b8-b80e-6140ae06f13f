#!/usr/bin/env python3
"""
Meal plans routes for Preg and Baby Care application.
"""

from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from models.database import get_db_connection
from datetime import datetime

meal_plans_bp = Blueprint('meal_plans', __name__)

@meal_plans_bp.route('/daily', methods=['GET'])
def get_daily_meal_plans():
    """Get daily meal plans."""
    try:
        trimester = request.args.get('trimester', type=int)
        day_of_week = request.args.get('day_of_week', type=int)
        week_number = request.args.get('week_number', type=int, default=1)
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        query = 'SELECT * FROM daily_meal_plans WHERE is_active = 1'
        params = []
        
        if trimester:
            query += ' AND trimester = ?'
            params.append(trimester)
        
        if day_of_week:
            query += ' AND day_of_week = ?'
            params.append(day_of_week)
        
        if week_number:
            query += ' AND week_number = ?'
            params.append(week_number)
        
        query += ' ORDER BY day_of_week, meal_type'
        
        cursor.execute(query, params)
        meal_plans = cursor.fetchall()
        conn.close()
        
        # Group by day and meal type
        grouped_plans = {}
        for plan in meal_plans:
            day = plan['day_of_week']
            meal_type = plan['meal_type']
            
            if day not in grouped_plans:
                grouped_plans[day] = {}
            
            grouped_plans[day][meal_type] = dict(plan)
        
        return jsonify({
            'meal_plans': grouped_plans,
            'filters': {
                'trimester': trimester,
                'day_of_week': day_of_week,
                'week_number': week_number
            }
        }), 200
        
    except Exception as e:
        return jsonify({'error': f'Failed to get meal plans: {str(e)}'}), 500

@meal_plans_bp.route('/weekly', methods=['GET'])
@jwt_required()
def get_weekly_meal_plans():
    """Get personalized weekly meal plans."""
    try:
        user_id = get_jwt_identity()
        week_number = request.args.get('week_number', type=int, default=1)
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Get user information
        cursor.execute('''
            SELECT is_pregnant, due_date, baby_birth_date
            FROM users WHERE id = ?
        ''', (user_id,))
        
        user = cursor.fetchone()
        if not user:
            conn.close()
            return jsonify({'error': 'User not found'}), 404
        
        trimester = None
        if user['is_pregnant'] and user['due_date']:
            # Calculate trimester
            due_date = datetime.strptime(user['due_date'], '%Y-%m-%d')
            today = datetime.now()
            weeks_pregnant = (280 - (due_date - today).days) // 7
            
            if weeks_pregnant <= 12:
                trimester = 1
            elif weeks_pregnant <= 28:
                trimester = 2
            else:
                trimester = 3
        
        # Get meal plans for the week
        query = '''
            SELECT * FROM daily_meal_plans 
            WHERE is_active = 1 AND week_number = ?
        '''
        params = [week_number]
        
        if trimester:
            query += ' AND (trimester = ? OR trimester IS NULL)'
            params.append(trimester)
        
        query += ' ORDER BY day_of_week, meal_type'
        
        cursor.execute(query, params)
        meal_plans = cursor.fetchall()
        conn.close()
        
        # Group by day
        weekly_plan = {}
        days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
        
        for i in range(1, 8):
            day_name = days[i-1]
            weekly_plan[day_name] = {
                'day_number': i,
                'breakfast': None,
                'lunch': None,
                'dinner': None,
                'snacks': []
            }
        
        for plan in meal_plans:
            day_name = days[plan['day_of_week'] - 1]
            meal_type = plan['meal_type']
            
            if meal_type == 'snack':
                weekly_plan[day_name]['snacks'].append(dict(plan))
            else:
                weekly_plan[day_name][meal_type] = dict(plan)
        
        return jsonify({
            'weekly_plan': weekly_plan,
            'week_number': week_number,
            'trimester': trimester,
            'user_info': {
                'is_pregnant': bool(user['is_pregnant'])
            }
        }), 200
        
    except Exception as e:
        return jsonify({'error': f'Failed to get weekly meal plans: {str(e)}'}), 500

@meal_plans_bp.route('/today', methods=['GET'])
@jwt_required()
def get_today_meal_plan():
    """Get today's meal plan."""
    try:
        user_id = get_jwt_identity()
        today = datetime.now()
        day_of_week = today.weekday() + 1  # Monday = 1
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Get user information
        cursor.execute('''
            SELECT is_pregnant, due_date
            FROM users WHERE id = ?
        ''', (user_id,))
        
        user = cursor.fetchone()
        if not user:
            conn.close()
            return jsonify({'error': 'User not found'}), 404
        
        trimester = None
        if user['is_pregnant'] and user['due_date']:
            # Calculate trimester
            due_date = datetime.strptime(user['due_date'], '%Y-%m-%d')
            weeks_pregnant = (280 - (due_date - today).days) // 7
            
            if weeks_pregnant <= 12:
                trimester = 1
            elif weeks_pregnant <= 28:
                trimester = 2
            else:
                trimester = 3
        
        # Get today's meal plans
        query = '''
            SELECT * FROM daily_meal_plans 
            WHERE is_active = 1 AND day_of_week = ?
        '''
        params = [day_of_week]
        
        if trimester:
            query += ' AND (trimester = ? OR trimester IS NULL)'
            params.append(trimester)
        
        query += ' ORDER BY meal_type'
        
        cursor.execute(query, params)
        meal_plans = cursor.fetchall()
        conn.close()
        
        # Organize by meal type
        today_plan = {
            'breakfast': None,
            'lunch': None,
            'dinner': None,
            'snacks': []
        }
        
        for plan in meal_plans:
            meal_type = plan['meal_type']
            if meal_type == 'snack':
                today_plan['snacks'].append(dict(plan))
            else:
                today_plan[meal_type] = dict(plan)
        
        return jsonify({
            'today_plan': today_plan,
            'date': today.strftime('%Y-%m-%d'),
            'day_of_week': day_of_week,
            'trimester': trimester
        }), 200
        
    except Exception as e:
        return jsonify({'error': f'Failed to get today\'s meal plan: {str(e)}'}), 500

@meal_plans_bp.route('/shopping-list', methods=['GET'])
@jwt_required()
def get_shopping_list():
    """Generate shopping list for the week."""
    try:
        user_id = get_jwt_identity()
        week_number = request.args.get('week_number', type=int, default=1)
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Get user's trimester
        cursor.execute('''
            SELECT is_pregnant, due_date
            FROM users WHERE id = ?
        ''', (user_id,))
        
        user = cursor.fetchone()
        if not user:
            conn.close()
            return jsonify({'error': 'User not found'}), 404
        
        trimester = None
        if user['is_pregnant'] and user['due_date']:
            due_date = datetime.strptime(user['due_date'], '%Y-%m-%d')
            today = datetime.now()
            weeks_pregnant = (280 - (due_date - today).days) // 7
            
            if weeks_pregnant <= 12:
                trimester = 1
            elif weeks_pregnant <= 28:
                trimester = 2
            else:
                trimester = 3
        
        # Get all meal plans for the week
        query = '''
            SELECT ingredients FROM daily_meal_plans 
            WHERE is_active = 1 AND week_number = ? AND ingredients IS NOT NULL
        '''
        params = [week_number]
        
        if trimester:
            query += ' AND (trimester = ? OR trimester IS NULL)'
            params.append(trimester)
        
        cursor.execute(query, params)
        meal_plans = cursor.fetchall()
        conn.close()
        
        # Collect all ingredients
        all_ingredients = []
        for plan in meal_plans:
            if plan['ingredients']:
                ingredients = plan['ingredients'].split(',')
                all_ingredients.extend([ing.strip() for ing in ingredients])
        
        # Remove duplicates and organize
        unique_ingredients = list(set(all_ingredients))
        unique_ingredients.sort()
        
        # Categorize ingredients (basic categorization)
        categorized = {
            'Proteins': [],
            'Vegetables': [],
            'Fruits': [],
            'Grains': [],
            'Dairy': [],
            'Others': []
        }
        
        protein_keywords = ['chicken', 'fish', 'beef', 'eggs', 'tofu', 'beans', 'lentils']
        vegetable_keywords = ['carrot', 'broccoli', 'spinach', 'tomato', 'onion', 'pepper']
        fruit_keywords = ['apple', 'banana', 'orange', 'berry', 'grape', 'mango']
        grain_keywords = ['rice', 'bread', 'pasta', 'oats', 'quinoa', 'wheat']
        dairy_keywords = ['milk', 'cheese', 'yogurt', 'butter']
        
        for ingredient in unique_ingredients:
            ingredient_lower = ingredient.lower()
            categorized_flag = False
            
            for keyword in protein_keywords:
                if keyword in ingredient_lower:
                    categorized['Proteins'].append(ingredient)
                    categorized_flag = True
                    break
            
            if not categorized_flag:
                for keyword in vegetable_keywords:
                    if keyword in ingredient_lower:
                        categorized['Vegetables'].append(ingredient)
                        categorized_flag = True
                        break
            
            if not categorized_flag:
                for keyword in fruit_keywords:
                    if keyword in ingredient_lower:
                        categorized['Fruits'].append(ingredient)
                        categorized_flag = True
                        break
            
            if not categorized_flag:
                for keyword in grain_keywords:
                    if keyword in ingredient_lower:
                        categorized['Grains'].append(ingredient)
                        categorized_flag = True
                        break
            
            if not categorized_flag:
                for keyword in dairy_keywords:
                    if keyword in ingredient_lower:
                        categorized['Dairy'].append(ingredient)
                        categorized_flag = True
                        break
            
            if not categorized_flag:
                categorized['Others'].append(ingredient)
        
        return jsonify({
            'shopping_list': categorized,
            'total_items': len(unique_ingredients),
            'week_number': week_number
        }), 200
        
    except Exception as e:
        return jsonify({'error': f'Failed to generate shopping list: {str(e)}'}), 500
