/* Responsive CSS for Preg and Baby Care Application */

/* Breakpoints */
/* 
  xs: 0px
  sm: 576px
  md: 768px
  lg: 992px
  xl: 1200px
  2xl: 1400px
*/

/* Large Desktop (1200px and up) */
@media (min-width: 1200px) {
    .container {
        max-width: 1140px;
    }
    
    .hero-title {
        font-size: 4rem;
    }
    
    .section-title {
        font-size: 3.5rem;
    }
}

/* Desktop (992px to 1199px) */
@media (max-width: 1199px) {
    .hero-container {
        gap: var(--spacing-2xl);
    }
    
    .hero-illustration {
        width: 250px;
        height: 250px;
    }
    
    .hero-icon {
        font-size: 5rem;
    }
}

/* Tablet (768px to 991px) */
@media (max-width: 991px) {
    /* Navigation */
    .nav-menu {
        position: fixed;
        top: 4rem;
        left: 0;
        right: 0;
        background-color: var(--white);
        border-top: 1px solid var(--gray-200);
        box-shadow: var(--shadow-lg);
        flex-direction: column;
        padding: var(--spacing-lg);
        gap: var(--spacing-md);
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        transition: all var(--transition-normal);
        z-index: var(--z-dropdown);
    }
    
    .nav-menu.active {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }
    
    .nav-section {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-sm);
    }
    
    .nav-dropdown .dropdown-menu {
        position: static;
        opacity: 1;
        visibility: visible;
        transform: none;
        box-shadow: none;
        border: none;
        background-color: var(--gray-50);
        margin-top: var(--spacing-sm);
        border-radius: var(--border-radius-md);
    }
    
    .nav-toggle {
        display: flex;
    }
    
    .nav-toggle.active .bar:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }
    
    .nav-toggle.active .bar:nth-child(2) {
        opacity: 0;
    }
    
    .nav-toggle.active .bar:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }
    
    /* Hero Section */
    .hero {
        padding: 6rem 0 3rem;
    }
    
    .hero-container {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
        text-align: center;
    }
    
    .hero-content {
        max-width: none;
        order: 2;
    }
    
    .hero-image {
        order: 1;
    }
    
    .hero-title {
        font-size: var(--font-size-4xl);
    }
    
    .hero-illustration {
        width: 200px;
        height: 200px;
    }
    
    .hero-icon {
        font-size: 4rem;
    }
    
    /* Features */
    .features-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: var(--spacing-lg);
    }
    
    /* Stats */
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-lg);
    }
    
    /* Auth Pages */
    .auth-container {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
    }
    
    .auth-benefits {
        order: -1;
        padding: var(--spacing-lg);
        background-color: rgba(255, 255, 255, 0.9);
        border-radius: var(--border-radius-lg);
    }
    
    .form-row {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
    
    /* Footer */
    .footer-content {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-lg);
    }
}

/* Mobile Large (576px to 767px) */
@media (max-width: 767px) {
    /* Typography */
    .hero-title {
        font-size: var(--font-size-3xl);
    }
    
    .section-title {
        font-size: var(--font-size-3xl);
    }
    
    .cta-title {
        font-size: var(--font-size-3xl);
    }
    
    /* Spacing */
    .hero {
        padding: 5rem 0 2rem;
    }
    
    .features,
    .stats,
    .cta {
        padding: var(--spacing-2xl) 0;
    }
    
    .section-header {
        margin-bottom: var(--spacing-2xl);
    }
    
    /* Hero */
    .hero-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .hero-actions .btn {
        width: 100%;
        max-width: 300px;
    }
    
    .hero-illustration {
        width: 180px;
        height: 180px;
    }
    
    .hero-icon {
        font-size: 3.5rem;
    }
    
    .floating-element {
        width: 2.5rem;
        height: 2.5rem;
    }
    
    .floating-element i {
        font-size: var(--font-size-base);
    }
    
    /* Features */
    .features-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }
    
    .feature-card {
        padding: var(--spacing-lg);
    }
    
    /* Stats */
    .stats-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
        text-align: center;
    }
    
    .stat-number {
        font-size: var(--font-size-3xl);
    }
    
    /* CTA */
    .cta-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .cta-actions .btn {
        width: 100%;
        max-width: 300px;
    }
    
    /* Auth Pages */
    .auth-main {
        padding: 5rem 0 1rem;
    }
    
    .auth-card {
        padding: var(--spacing-xl);
    }
    
    .auth-title {
        font-size: var(--font-size-2xl);
    }
    
    .social-login {
        gap: var(--spacing-sm);
    }
    
    /* Footer */
    .footer {
        padding: var(--spacing-2xl) 0 var(--spacing-lg);
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
        text-align: center;
    }
    
    .social-links {
        justify-content: center;
    }
    
    .footer-minimal .footer-content {
        flex-direction: column;
        text-align: center;
    }
    
    .footer-minimal .footer-links {
        flex-wrap: wrap;
        justify-content: center;
    }
}

/* Mobile Small (up to 575px) */
@media (max-width: 575px) {
    /* Container */
    .container {
        padding: 0 var(--spacing-sm);
    }
    
    /* Navigation */
    .nav-container {
        padding: 0 var(--spacing-sm);
    }
    
    .brand-text {
        display: none;
    }
    
    /* Typography */
    .hero-title {
        font-size: var(--font-size-2xl);
    }
    
    .section-title {
        font-size: var(--font-size-2xl);
    }
    
    .cta-title {
        font-size: var(--font-size-2xl);
    }
    
    .auth-title {
        font-size: var(--font-size-xl);
    }
    
    /* Spacing */
    .hero {
        padding: 4rem 0 2rem;
    }
    
    .features,
    .stats,
    .cta {
        padding: var(--spacing-xl) 0;
    }
    
    /* Hero */
    .hero-container {
        gap: var(--spacing-lg);
    }
    
    .hero-illustration {
        width: 150px;
        height: 150px;
    }
    
    .hero-icon {
        font-size: 3rem;
    }
    
    .floating-element {
        width: 2rem;
        height: 2rem;
    }
    
    .floating-element i {
        font-size: var(--font-size-sm);
    }
    
    /* Features */
    .feature-card {
        padding: var(--spacing-md);
    }
    
    .feature-icon {
        width: 3rem;
        height: 3rem;
    }
    
    .feature-icon i {
        font-size: var(--font-size-lg);
    }
    
    /* Auth Pages */
    .auth-container {
        padding: 0 var(--spacing-sm);
    }
    
    .auth-card {
        padding: var(--spacing-lg);
    }
    
    .auth-benefits {
        padding: var(--spacing-md);
    }
    
    .form-options {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }
    
    /* Buttons */
    .btn-large {
        padding: var(--spacing-md) var(--spacing-lg);
        font-size: var(--font-size-sm);
    }
    
    .hero-actions .btn,
    .cta-actions .btn {
        width: 100%;
    }
}

/* Extra Small Mobile (up to 375px) */
@media (max-width: 375px) {
    /* Typography */
    .hero-title {
        font-size: var(--font-size-xl);
    }
    
    .section-title {
        font-size: var(--font-size-xl);
    }
    
    /* Hero */
    .hero-illustration {
        width: 120px;
        height: 120px;
    }
    
    .hero-icon {
        font-size: 2.5rem;
    }
    
    /* Auth */
    .auth-card {
        padding: var(--spacing-md);
    }
    
    .benefits-list {
        gap: var(--spacing-md);
    }
    
    .benefit-item {
        gap: var(--spacing-sm);
    }
}

/* Landscape Mobile */
@media (max-height: 500px) and (orientation: landscape) {
    .hero {
        padding: 2rem 0 1rem;
    }
    
    .auth-main {
        padding: 2rem 0 1rem;
    }
    
    .hero-illustration {
        width: 120px;
        height: 120px;
    }
    
    .floating-element {
        display: none;
    }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    /* Optimize for high DPI displays */
    .hero-illustration {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .floating-element {
        animation: none;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    /* This can be expanded when dark mode is implemented */
    .auth-page {
        background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
    }
}
